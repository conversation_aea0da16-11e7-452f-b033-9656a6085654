#!/bin/bash

# UniversalWallet UI Mockups - Modern Development Environment Setup
# This script sets up the modern development environment with Vite and modern tools

echo "🚀 Setting up UniversalWallet UI Mockups - Modern Development Environment"
echo "=================================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    echo "   Please update Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the ui_mockups directory."
    exit 1
fi

echo "✅ Found package.json"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
echo "================================"

if command -v pnpm &> /dev/null; then
    echo "Using pnpm..."
    pnpm install
elif command -v yarn &> /dev/null; then
    echo "Using yarn..."
    yarn install
else
    echo "Using npm..."
    npm install
fi

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo ""
    echo "📝 Creating .env file..."
    cat > .env << EOF
# UniversalWallet UI Mockups Environment Variables
NODE_ENV=development
VITE_APP_NAME=UniversalWallet UI Mockups
VITE_APP_VERSION=1.0.0
VITE_DEV_PORT=8000
EOF
    echo "✅ Created .env file"
fi

# Run initial linting and formatting
echo ""
echo "🔧 Running code quality checks..."
echo "=================================="

echo "Running ESLint..."
if command -v pnpm &> /dev/null; then
    pnpm run lint --fix
elif command -v yarn &> /dev/null; then
    yarn lint --fix
else
    npm run lint -- --fix
fi

echo "Running Prettier..."
if command -v pnpm &> /dev/null; then
    pnpm run format
elif command -v yarn &> /dev/null; then
    yarn format
else
    npm run format
fi

echo "✅ Code quality checks completed"

# Success message
echo ""
echo "🎉 Setup completed successfully!"
echo "================================"
echo ""
echo "🚀 To start the development server:"
echo "   npm run dev"
echo "   # or"
echo "   yarn dev"
echo "   # or"
echo "   pnpm dev"
echo ""
echo "📖 Available commands:"
echo "   npm run dev      - Start development server"
echo "   npm run build    - Build for production"
echo "   npm run preview  - Preview production build"
echo "   npm run lint     - Run ESLint"
echo "   npm run format   - Format code with Prettier"
echo ""
echo "🌐 The server will be available at: http://localhost:8000"
echo ""
echo "📚 For more information, see README-DEV.md"
echo ""
echo "Happy coding! 🎨✨"

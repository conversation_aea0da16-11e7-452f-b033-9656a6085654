// Business Dashboard Tests
const { test, expect } = require('@playwright/test');
const { waitForAnimations, hasGlassmorphism, testLoadingState } = require('../utils/test-helpers');

test.describe('Business Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/business/');
    await waitForAnimations(page);
  });

  test('should load business dashboard successfully @desktop @smoke', async ({ page }) => {
    await expect(page).toHaveTitle(/UniversalWallet.*Business Dashboard/);
    
    // Check main layout elements
    await expect(page.locator('.sidebar')).toBeVisible();
    await expect(page.locator('.main-content')).toBeVisible();
    await expect(page.locator('.top-header')).toBeVisible();
    await expect(page.locator('.page-content')).toBeVisible();
    
    // Check business badge
    await expect(page.locator('.business-badge')).toBeVisible();
    await expect(page.locator('.business-badge')).toHaveText('Business');
  });

  test('should have business-specific navigation @desktop @navigation', async ({ page }) => {
    const menuItems = page.locator('.menu-item');
    
    // Check business-specific menu items
    const expectedMenuItems = [
      'Dashboard', 'Business Accounts', 'Analytics',
      'Bulk Payments', 'Invoicing', 'Payroll', 'Supplier Payments',
      'Team Management', 'Approvals', 'Reports', 'Transactions', 'Settings'
    ];
    
    await expect(menuItems).toHaveCount(expectedMenuItems.length);
    
    // Check for business-specific sections
    const menuSections = page.locator('.menu-section .menu-title');
    const expectedSections = ['Overview', 'Payments', 'Management', 'Account'];
    
    for (let i = 0; i < expectedSections.length; i++) {
      await expect(menuSections.nth(i)).toHaveText(expectedSections[i]);
    }
  });

  test('should display business metrics correctly @desktop @content', async ({ page }) => {
    const metricsSection = page.locator('.business-metrics');
    await expect(metricsSection).toBeVisible();
    
    const metricCards = metricsSection.locator('.metric-card');
    await expect(metricCards).toHaveCount(4);
    
    // Check primary metric card
    const primaryCard = metricCards.first();
    await expect(primaryCard).toHaveClass(/primary/);
    await expect(primaryCard.locator('.metric-title')).toHaveText('Total Business Balance');
    await expect(primaryCard.locator('.amount')).toBeVisible();
    await expect(primaryCard.locator('.metric-change')).toBeVisible();
    
    // Check other metric cards
    const expectedMetrics = ['Monthly Revenue', 'Pending Payments', 'Team Members'];
    for (let i = 1; i < expectedMetrics.length + 1; i++) {
      const card = metricCards.nth(i);
      await expect(card.locator('.metric-title')).toHaveText(expectedMetrics[i - 1]);
      await expect(card.locator('.amount')).toBeVisible();
    }
  });

  test('should have business quick actions @desktop @content', async ({ page }) => {
    const quickActions = page.locator('.quick-actions');
    await expect(quickActions).toBeVisible();
    
    const actionCards = quickActions.locator('.action-card');
    await expect(actionCards).toHaveCount(6);
    
    const expectedActions = [
      'Bulk Payment', 'Create Invoice', 'Run Payroll',
      'Expense Report', 'Invite Team', 'API Integration'
    ];
    
    for (let i = 0; i < expectedActions.length; i++) {
      const card = actionCards.nth(i);
      await expect(card.locator('.action-title')).toHaveText(expectedActions[i]);
      await expect(card.locator('.action-icon')).toBeVisible();
    }
  });

  test('should have working business action buttons @desktop @interactions', async ({ page }) => {
    const actionCards = page.locator('.action-card');
    
    // Test bulk payment action
    const bulkPaymentAction = page.locator('.action-card[data-action="bulk-payment"]');
    if (await bulkPaymentAction.count() > 0) {
      const loadingResult = await testLoadingState(page, '.action-card[data-action="bulk-payment"]');
      expect(loadingResult.hasLoadingIndicator).toBe(true);
    }
    
    // Test create invoice action
    const invoiceAction = page.locator('.action-card[data-action="create-invoice"]');
    if (await invoiceAction.count() > 0) {
      await invoiceAction.click();
      await page.waitForTimeout(1000);
    }
  });

  test('should display business accounts @desktop @content', async ({ page }) => {
    const accountsSection = page.locator('.business-accounts');
    await expect(accountsSection).toBeVisible();
    
    const accountCards = accountsSection.locator('.account-card');
    await expect(accountCards).toHaveCount(3);
    
    // Check primary business account
    const primaryAccount = accountCards.first();
    await expect(primaryAccount).toHaveClass(/primary-account/);
    await expect(primaryAccount.locator('.provider-name')).toHaveText('TechCorp Business');
    await expect(primaryAccount.locator('.account-number')).toHaveText('Primary Account');
    
    // Check other business accounts
    const expectedAccounts = ['EcoCash Business', 'ZB Business'];
    for (let i = 1; i < expectedAccounts.length + 1; i++) {
      const card = accountCards.nth(i);
      await expect(card.locator('.provider-name')).toContainText(expectedAccounts[i - 1]);
      await expect(card.locator('.balance-value')).toBeVisible();
    }
  });

  test('should display business activity @desktop @content', async ({ page }) => {
    const activitySection = page.locator('.recent-activity');
    await expect(activitySection).toBeVisible();
    
    const activityItems = activitySection.locator('.activity-item');
    await expect(activityItems).toHaveCount(4);
    
    // Check activity types
    const expectedActivities = [
      'Monthly Payroll Processed',
      'Invoice Payment Received',
      'Supplier Payment',
      'Bulk Payment to Contractors'
    ];
    
    for (let i = 0; i < expectedActivities.length; i++) {
      const item = activityItems.nth(i);
      await expect(item.locator('.activity-title')).toContainText(expectedActivities[i]);
      await expect(item.locator('.activity-icon')).toBeVisible();
      await expect(item.locator('.activity-amount')).toBeVisible();
      await expect(item.locator('.activity-status')).toBeVisible();
    }
  });

  test('should display pending approvals @desktop @content', async ({ page }) => {
    const approvalsSection = page.locator('.pending-approvals');
    await expect(approvalsSection).toBeVisible();
    
    // Check approval count badge
    const approvalCount = approvalsSection.locator('.approval-count');
    await expect(approvalCount).toHaveText('3 pending');
    
    const approvalItems = approvalsSection.locator('.approval-item');
    await expect(approvalItems).toHaveCount(3);
    
    // Check approval structure
    for (let i = 0; i < Math.min(await approvalItems.count(), 2); i++) {
      const item = approvalItems.nth(i);
      await expect(item.locator('.approval-title')).toBeVisible();
      await expect(item.locator('.approval-subtitle')).toBeVisible();
      await expect(item.locator('.approval-requester')).toBeVisible();
      await expect(item.locator('.approval-actions .btn')).toHaveCount(2);
    }
  });

  test('should have working approval workflow @desktop @interactions', async ({ page }) => {
    const approvalItems = page.locator('.approval-item');
    
    if (await approvalItems.count() > 0) {
      const firstApproval = approvalItems.first();
      const approveButton = firstApproval.locator('.btn-primary');
      const rejectButton = firstApproval.locator('.btn-outline');
      
      // Test approve button
      await approveButton.click();
      await page.waitForTimeout(2000); // Wait for approval processing
      
      // Check if approval was processed (item should be removed or updated)
      const remainingApprovals = page.locator('.approval-item');
      const newCount = await remainingApprovals.count();
      expect(newCount).toBeLessThan(3);
    }
  });

  test('should have notification badges @desktop @content', async ({ page }) => {
    // Check approval notification in sidebar
    const approvalsMenuItem = page.locator('.menu-item[data-page="approvals"]');
    const notificationBadge = approvalsMenuItem.locator('.notification-count');
    
    await expect(notificationBadge).toBeVisible();
    await expect(notificationBadge).toHaveText('3');
    
    // Check header notification
    const headerNotification = page.locator('.header-actions .notification-badge');
    await expect(headerNotification).toBeVisible();
  });

  test('should have business user profile @desktop @content', async ({ page }) => {
    const userProfile = page.locator('.user-profile');
    await expect(userProfile).toBeVisible();
    
    // Check business user info
    await expect(userProfile.locator('.user-name')).toHaveText('TechCorp Ltd');
    await expect(userProfile.locator('.user-status')).toHaveText('Business Account');
    await expect(userProfile.locator('.user-avatar img')).toBeVisible();
  });

  test('should have working metric toggle @desktop @interactions', async ({ page }) => {
    const metricToggle = page.locator('.metric-toggle').first();
    const amounts = page.locator('.amount, .balance-value');
    
    if (await metricToggle.count() > 0) {
      // Get initial state
      const firstAmount = amounts.first();
      const initialFilter = await firstAmount.evaluate(el => 
        window.getComputedStyle(el).filter
      );
      
      // Toggle metric visibility
      await metricToggle.click();
      await page.waitForTimeout(500);
      
      // Check if filter changed (blur effect)
      const newFilter = await firstAmount.evaluate(el => 
        window.getComputedStyle(el).filter
      );
      
      expect(newFilter).not.toBe(initialFilter);
    }
  });

  test('should have glassmorphism effects @desktop @visual', async ({ page }) => {
    // Check sidebar glassmorphism
    const sidebar = page.locator('.sidebar');
    expect(await hasGlassmorphism(sidebar)).toBe(true);
    
    // Check metric cards glassmorphism
    const metricCards = page.locator('.metric-card:not(.primary)');
    if (await metricCards.count() > 0) {
      expect(await hasGlassmorphism(metricCards.first())).toBe(true);
    }
    
    // Check business account cards glassmorphism
    const accountCards = page.locator('.account-card:not(.primary-account)');
    if (await accountCards.count() > 0) {
      expect(await hasGlassmorphism(accountCards.first())).toBe(true);
    }
  });

  test('should be responsive @mobile @responsive', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    // Check mobile sidebar behavior
    const sidebar = page.locator('.sidebar');
    const mobileSidebarToggle = page.locator('.mobile-sidebar-toggle');
    
    await expect(mobileSidebarToggle).toBeVisible();
    
    // Check metric cards stack on mobile
    const metricCards = page.locator('.metric-card');
    if (await metricCards.count() >= 2) {
      const firstCard = metricCards.first();
      const secondCard = metricCards.nth(1);
      
      const firstCardBox = await firstCard.boundingBox();
      const secondCardBox = await secondCard.boundingBox();
      
      // Cards should stack vertically on mobile
      expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 10);
    }
  });

  test('should handle business navigation @desktop @navigation', async ({ page }) => {
    const menuItems = page.locator('.menu-item');
    
    // Test business-specific navigation
    const bulkPaymentsItem = page.locator('.menu-item[data-page="bulk-payments"]');
    if (await bulkPaymentsItem.count() > 0) {
      await bulkPaymentsItem.click();
      await page.waitForTimeout(500);
      
      const pageTitle = page.locator('#page-title');
      await expect(pageTitle).toHaveText('Bulk Payments');
    }
    
    // Test team management navigation
    const teamItem = page.locator('.menu-item[data-page="team"]');
    if (await teamItem.count() > 0) {
      await teamItem.click();
      await page.waitForTimeout(500);
      
      const pageTitle = page.locator('#page-title');
      await expect(pageTitle).toHaveText('Team Management');
    }
  });

  test('should load without JavaScript errors @desktop @smoke', async ({ page }) => {
    const errors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.reload();
    await waitForAnimations(page);
    
    expect(errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_')
    )).toHaveLength(0);
  });

  test('should have working header actions @desktop @interactions', async ({ page }) => {
    const headerActions = page.locator('.header-actions .action-btn');
    
    // Check business-specific header actions
    await expect(headerActions).toHaveCount(4);
    
    // Test each header action
    for (let i = 0; i < Math.min(await headerActions.count(), 3); i++) {
      const button = headerActions.nth(i);
      await button.click();
      await page.waitForTimeout(300);
    }
  });

  test('should display correct business branding @desktop @content', async ({ page }) => {
    // Check business badge styling
    const businessBadge = page.locator('.business-badge');
    await expect(businessBadge).toBeVisible();
    
    const badgeColor = await businessBadge.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    
    // Should have business-specific styling (different from individual)
    expect(badgeColor).not.toBe('rgba(0, 0, 0, 0)');
  });
});

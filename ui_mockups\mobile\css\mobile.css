/* Mobile App Styles */

/* Reset and Base */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
}

html, body {
  height: 100%;
  overflow: hidden;
  font-family: var(--font-family-primary);
  background: var(--color-neutral-900);
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

/* Mobile App Container */
.mobile-app {
  width: 375px;
  height: 812px;
  max-width: 100vw;
  max-height: 100vh;
  margin: 0 auto;
  background: linear-gradient(135deg, var(--color-neutral-50), var(--color-neutral-100));
  border-radius: 40px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

/* Status Bar */
.status-bar {
  height: 44px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  position: relative;
  z-index: 100;
}

.status-left .time {
  font-weight: var(--font-weight-bold);
}

.status-right {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.status-right i {
  font-size: var(--font-size-sm);
}

.battery {
  width: 24px;
  height: 12px;
  border: 1px solid var(--color-text-primary);
  border-radius: 2px;
  position: relative;
}

.battery::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: var(--color-text-primary);
  border-radius: 0 1px 1px 0;
}

.battery-level {
  width: 80%;
  height: 100%;
  background: var(--color-success);
  border-radius: 1px;
}

/* App Header */
.app-header {
  padding: var(--space-4) var(--space-6);
  background: transparent;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-3);
}

.back-to-hub-mobile {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--glass-bg);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: all var(--transition-base);
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.back-to-hub-mobile:hover {
  background: var(--color-primary);
  color: white;
  transform: scale(1.05);
}

.user-greeting {
  flex: 1;
}

.greeting-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-1);
}

.user-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.header-actions {
  display: flex;
  gap: var(--space-3);
}

.header-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
}

.header-btn:active {
  transform: scale(0.95);
}

.header-btn img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.notification-dot {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 8px;
  height: 8px;
  background: var(--color-error);
  border-radius: 50%;
  border: 2px solid var(--color-neutral-50);
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 0 var(--space-6);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: var(--space-20);
}

/* Balance Section */
.balance-section {
  margin-bottom: var(--space-8);
}

.balance-card {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  color: white;
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
  position: relative;
  z-index: 2;
}

.balance-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.balance-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
  opacity: 0.8;
}

.balance-toggle:active {
  transform: scale(0.95);
  opacity: 1;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  gap: var(--space-1);
  margin-bottom: var(--space-6);
  position: relative;
  z-index: 2;
}

.currency {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  opacity: 0.9;
}

.amount {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

.balance-accounts {
  display: flex;
  gap: var(--space-3);
  position: relative;
  z-index: 2;
}

.account-chip {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.chip-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: white;
}

.account-chip.ecocash .chip-icon {
  background: linear-gradient(135deg, #FF6B35, #F7931E);
}

.account-chip.onemoney .chip-icon {
  background: linear-gradient(135deg, #1E3A8A, #3B82F6);
}

.account-chip.bank .chip-icon {
  background: linear-gradient(135deg, #059669, #10B981);
}

/* Quick Actions */
.quick-actions {
  margin-bottom: var(--space-8);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-4);
}

.action-btn {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-6) var(--space-4);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  transition: all var(--transition-base);
  text-align: center;
}

.action-btn:active {
  transform: scale(0.95);
  background: var(--glass-bg-strong);
}

.action-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-neutral-900);
  font-size: var(--font-size-lg);
}

.action-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.section-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.see-all-btn {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
}

.see-all-btn:active {
  transform: scale(0.95);
  background: var(--color-primary-alpha);
}

/* Services Section */
.services-section {
  margin-bottom: var(--space-8);
}

.services-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.service-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-base);
}

.service-card:active {
  transform: scale(0.98);
  background: var(--glass-bg-strong);
}

.service-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.service-content {
  flex: 1;
}

.service-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.service-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.service-arrow {
  color: var(--color-text-tertiary);
  font-size: var(--font-size-sm);
}

/* Transactions Section */
.transactions-section {
  margin-bottom: var(--space-8);
}

.transactions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.transaction-item {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-base);
}

.transaction-item:active {
  transform: scale(0.98);
  background: var(--glass-bg-strong);
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.transaction-icon.received {
  background: linear-gradient(135deg, var(--color-success), var(--color-success-dark));
}

.transaction-icon.sent {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
}

.transaction-icon.bill {
  background: linear-gradient(135deg, var(--color-warning), var(--color-warning-dark));
}

.transaction-icon.group {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
}

.transaction-details {
  flex: 1;
}

.transaction-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.transaction-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.transaction-amount {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
}

.transaction-amount.positive {
  color: var(--color-success);
}

.transaction-amount.negative {
  color: var(--color-text-primary);
}

/* Bottom Navigation */
.bottom-nav {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 88px;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  border-top: 1px solid var(--glass-border);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: var(--space-2) var(--space-4);
  padding-bottom: calc(var(--space-2) + env(safe-area-inset-bottom));
}

.nav-item {
  background: none;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  cursor: pointer;
  transition: all var(--transition-base);
  padding: var(--space-2);
  border-radius: var(--radius-base);
  min-width: 60px;
}

.nav-item:active {
  transform: scale(0.95);
}

.nav-item i {
  font-size: var(--font-size-lg);
  color: var(--color-text-tertiary);
  transition: color var(--transition-base);
}

.nav-item span {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-base);
}

.nav-item.active i,
.nav-item.active span {
  color: var(--color-primary);
}

.nav-item.active {
  background: var(--color-primary-alpha);
}

/* Mobile Overlay */
.mobile-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-neutral-50);
  transform: translateY(100%);
  transition: transform var(--transition-base);
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.mobile-overlay.active {
  transform: translateY(0);
}

.overlay-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
}

.overlay-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.close-overlay {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
}

.close-overlay:active {
  transform: scale(0.95);
  background: var(--color-neutral-200);
}

.overlay-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
}

.notification-item {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-3);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.notification-text {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.notification-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* Touch Feedback */
.touch-feedback {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(6, 234, 175, 0.3) 0%, transparent 70%);
  pointer-events: none;
  transform: scale(0);
  z-index: 9999;
}

.touch-feedback.active {
  animation: touchRipple 0.6s ease-out;
}

@keyframes touchRipple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 375px) {
  .mobile-app {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }
}

@media (min-width: 376px) {
  .mobile-app {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}

# 3. Maintenance and Support

## 🔧 **Maintenance and Support Overview**

This document provides comprehensive guidelines for ongoing maintenance and support of the UniversalWallet platform, covering operational procedures, incident management, performance optimization, security updates, and long-term platform sustainability.

## 📅 **Maintenance Schedule and Procedures**

### **Maintenance Windows**
```yaml
# Scheduled Maintenance Calendar
Regular_Maintenance:
  frequency: weekly
  day: sunday
  time: "02:00-04:00 CAT"
  duration: 2_hours
  activities:
    - security_updates
    - performance_optimization
    - database_maintenance
    - log_rotation

Critical_Updates:
  frequency: as_needed
  notification_period: 24_hours
  max_duration: 4_hours
  activities:
    - security_patches
    - critical_bug_fixes
    - emergency_updates

Major_Releases:
  frequency: quarterly
  notification_period: 7_days
  maintenance_window: "saturday_22:00-sunday_06:00"
  duration: 8_hours
  activities:
    - feature_deployments
    - database_migrations
    - infrastructure_updates
    - comprehensive_testing
```

### **Pre-Maintenance Checklist**
```bash
#!/bin/bash
# scripts/pre-maintenance-checklist.sh

echo "🔍 Pre-Maintenance Checklist"
echo "=============================="

# 1. Backup verification
echo "1. Verifying recent backups..."
./scripts/verify-backups.sh

# 2. System health check
echo "2. Checking system health..."
kubectl get pods --all-namespaces | grep -v Running
kubectl top nodes
kubectl top pods --all-namespaces

# 3. Database health
echo "3. Checking database health..."
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT version();"
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "SELECT count(*) FROM pg_stat_activity;"

# 4. External service connectivity
echo "4. Testing external services..."
curl -f https://api.ecocash.co.zw/health || echo "EcoCash API unreachable"
curl -f https://api.onemoney.co.zw/health || echo "OneMoney API unreachable"

# 5. Monitoring systems
echo "5. Checking monitoring systems..."
curl -f http://prometheus:9090/-/healthy || echo "Prometheus unhealthy"
curl -f http://grafana:3000/api/health || echo "Grafana unhealthy"

# 6. Traffic analysis
echo "6. Analyzing current traffic..."
kubectl exec -n monitoring prometheus-0 -- promtool query instant 'sum(rate(http_requests_total[5m]))'

echo "✅ Pre-maintenance checklist completed"
```

---

## 🚨 **Incident Management**

### **Incident Response Procedures**
```yaml
# Incident Severity Levels
Severity_Levels:
  P1_Critical:
    description: "Complete service outage or security breach"
    response_time: 15_minutes
    escalation_time: 30_minutes
    notification:
      - on_call_engineer
      - engineering_manager
      - cto
      - ceo
    communication_frequency: every_15_minutes

  P2_High:
    description: "Major feature unavailable or significant performance degradation"
    response_time: 1_hour
    escalation_time: 2_hours
    notification:
      - on_call_engineer
      - engineering_manager
    communication_frequency: every_hour

  P3_Medium:
    description: "Minor feature issues or moderate performance impact"
    response_time: 4_hours
    escalation_time: 8_hours
    notification:
      - on_call_engineer
    communication_frequency: twice_daily

  P4_Low:
    description: "Cosmetic issues or minor inconveniences"
    response_time: 24_hours
    escalation_time: 72_hours
    notification:
      - engineering_team
    communication_frequency: daily
```

### **Incident Response Playbook**
```markdown
# Incident Response Playbook

## Phase 1: Detection and Initial Response (0-15 minutes)

### 1.1 Incident Detection
- [ ] Alert received via monitoring system
- [ ] User report received
- [ ] Internal discovery during routine checks

### 1.2 Initial Assessment
- [ ] Determine incident severity (P1-P4)
- [ ] Identify affected services/users
- [ ] Create incident ticket in Jira
- [ ] Notify appropriate stakeholders

### 1.3 Immediate Actions
- [ ] Acknowledge the incident
- [ ] Begin investigation
- [ ] Implement immediate workarounds if available
- [ ] Start incident communication

## Phase 2: Investigation and Diagnosis (15-60 minutes)

### 2.1 Gather Information
- [ ] Check monitoring dashboards
- [ ] Review recent deployments
- [ ] Analyze error logs
- [ ] Check external service status

### 2.2 Root Cause Analysis
- [ ] Identify the root cause
- [ ] Document findings
- [ ] Assess impact scope
- [ ] Determine fix complexity

### 2.3 Communication
- [ ] Update stakeholders on progress
- [ ] Provide ETA for resolution
- [ ] Update status page if needed

## Phase 3: Resolution (Variable)

### 3.1 Implement Fix
- [ ] Deploy hotfix if available
- [ ] Rollback if necessary
- [ ] Apply configuration changes
- [ ] Restart services if needed

### 3.2 Verification
- [ ] Confirm fix resolves the issue
- [ ] Monitor for any side effects
- [ ] Validate all systems are healthy
- [ ] Test critical user journeys

### 3.3 Communication
- [ ] Notify stakeholders of resolution
- [ ] Update status page
- [ ] Close incident ticket

## Phase 4: Post-Incident (24-48 hours)

### 4.1 Post-Mortem
- [ ] Schedule post-mortem meeting
- [ ] Document timeline of events
- [ ] Identify lessons learned
- [ ] Create action items for prevention

### 4.2 Follow-up
- [ ] Implement preventive measures
- [ ] Update monitoring/alerting
- [ ] Update documentation
- [ ] Share learnings with team
```

### **Incident Communication Templates**
```markdown
# Incident Communication Templates

## Initial Incident Notification
**Subject:** [P1] UniversalWallet Service Disruption - Investigating

We are currently investigating reports of service disruption affecting UniversalWallet users. 

**Impact:** Users may experience difficulty accessing the platform
**Status:** Investigating
**ETA:** Updates every 15 minutes

We will provide updates as more information becomes available.

## Incident Update
**Subject:** [P1] UniversalWallet Service Disruption - Update #2

**Update:** We have identified the root cause as a database connectivity issue and are implementing a fix.

**Impact:** Approximately 30% of users affected
**Status:** Implementing fix
**ETA:** Resolution expected within 30 minutes

## Incident Resolution
**Subject:** [RESOLVED] UniversalWallet Service Disruption

The service disruption has been resolved. All systems are now operating normally.

**Root Cause:** Database connection pool exhaustion due to increased traffic
**Resolution:** Increased connection pool size and implemented better connection management
**Duration:** 45 minutes (14:30 - 15:15 CAT)

A detailed post-mortem will be shared within 24 hours.
```

---

## 🔄 **Backup and Recovery Procedures**

### **Backup Strategy**
```yaml
# Backup Configuration
Database_Backups:
  full_backup:
    frequency: daily
    time: "01:00 CAT"
    retention: 30_days
    location: "s3://universalwallet-backups/database/full/"
    encryption: enabled
    compression: enabled

  incremental_backup:
    frequency: every_4_hours
    retention: 7_days
    location: "s3://universalwallet-backups/database/incremental/"

  point_in_time_recovery:
    enabled: true
    retention: 7_days
    wal_archiving: enabled

Application_Backups:
  configuration:
    frequency: before_each_deployment
    location: "s3://universalwallet-backups/config/"
    retention: 90_days

  logs:
    frequency: daily
    retention: 90_days
    location: "s3://universalwallet-backups/logs/"

Infrastructure_Backups:
  kubernetes_manifests:
    frequency: daily
    location: "git repository + s3 backup"
    retention: indefinite

  terraform_state:
    frequency: after_each_change
    location: "s3://universalwallet-terraform-state/"
    versioning: enabled
```

### **Disaster Recovery Procedures**
```bash
#!/bin/bash
# scripts/disaster-recovery.sh

set -euo pipefail

BACKUP_BUCKET="universalwallet-backups"
RECOVERY_TIMESTAMP="${1:-latest}"

echo "🚨 Starting Disaster Recovery Process"
echo "====================================="

# 1. Assess the situation
echo "1. Assessing current system state..."
kubectl cluster-info || echo "Kubernetes cluster unavailable"
aws s3 ls s3://${BACKUP_BUCKET}/ || echo "Backup storage unavailable"

# 2. Restore infrastructure
echo "2. Restoring infrastructure..."
cd terraform/production
terraform init
terraform plan -var="disaster_recovery=true"
terraform apply -auto-approve

# 3. Restore Kubernetes cluster
echo "3. Restoring Kubernetes cluster..."
aws eks update-kubeconfig --region af-south-1 --name universalwallet-production

# 4. Restore database
echo "4. Restoring database from backup..."
LATEST_BACKUP=$(aws s3 ls s3://${BACKUP_BUCKET}/database/full/ --recursive | sort | tail -n 1 | awk '{print $4}')
aws s3 cp s3://${BACKUP_BUCKET}/${LATEST_BACKUP} /tmp/database-backup.sql.gz
gunzip /tmp/database-backup.sql.gz

# Create new database instance if needed
kubectl apply -f k8s/database/postgresql-ha.yaml
kubectl wait --for=condition=ready pod -l app=postgresql --timeout=600s

# Restore data
kubectl exec -i postgresql-0 -- psql -U postgres < /tmp/database-backup.sql

# 5. Deploy applications
echo "5. Deploying applications..."
helm upgrade --install universalwallet-backend ./helm/backend \
  --namespace application \
  --values helm/backend/values-production.yaml \
  --wait

helm upgrade --install universalwallet-web ./helm/web \
  --namespace application \
  --values helm/web/values-production.yaml \
  --wait

# 6. Verify system health
echo "6. Verifying system health..."
./scripts/health-checks.sh

# 7. Run smoke tests
echo "7. Running smoke tests..."
./scripts/smoke-tests.sh

echo "✅ Disaster recovery completed successfully"
echo "📋 Next steps:"
echo "   1. Verify all services are operational"
echo "   2. Check data integrity"
echo "   3. Notify stakeholders of recovery"
echo "   4. Update monitoring and alerting"
echo "   5. Document lessons learned"
```

---

## 📊 **Performance Optimization**

### **Performance Monitoring and Tuning**
```java
// src/main/java/com/universalwallet/config/PerformanceConfig.java
@Configuration
@EnableConfigurationProperties(PerformanceProperties.class)
public class PerformanceConfig {
    
    @Bean
    @Primary
    public HikariDataSource dataSource(PerformanceProperties properties) {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(properties.getDatabase().getUrl());
        config.setUsername(properties.getDatabase().getUsername());
        config.setPassword(properties.getDatabase().getPassword());
        
        // Connection pool optimization
        config.setMaximumPoolSize(properties.getDatabase().getMaxPoolSize());
        config.setMinimumIdle(properties.getDatabase().getMinIdle());
        config.setConnectionTimeout(properties.getDatabase().getConnectionTimeout());
        config.setIdleTimeout(properties.getDatabase().getIdleTimeout());
        config.setMaxLifetime(properties.getDatabase().getMaxLifetime());
        config.setLeakDetectionThreshold(properties.getDatabase().getLeakDetectionThreshold());
        
        // Performance tuning
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        
        return new HikariDataSource(config);
    }
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .build();
    }
}
```

### **Database Optimization Scripts**
```sql
-- scripts/database-optimization.sql

-- Index optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_user_created 
ON transactions(user_id, created_at DESC) 
WHERE status IN ('completed', 'pending');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_reference_hash 
ON transactions USING hash(reference_number);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone_active 
ON users(phone_number) 
WHERE status = 'active';

-- Partition large tables
CREATE TABLE transactions_2024_q1 PARTITION OF transactions
FOR VALUES FROM ('2024-01-01') TO ('2024-04-01');

CREATE TABLE transactions_2024_q2 PARTITION OF transactions
FOR VALUES FROM ('2024-04-01') TO ('2024-07-01');

-- Update table statistics
ANALYZE transactions;
ANALYZE users;
ANALYZE linked_accounts;

-- Vacuum and reindex
VACUUM ANALYZE transactions;
REINDEX INDEX CONCURRENTLY idx_transactions_user_created;

-- Performance monitoring queries
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE tablename IN ('transactions', 'users', 'linked_accounts')
ORDER BY tablename, attname;

-- Slow query analysis
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE mean_time > 1000
ORDER BY mean_time DESC
LIMIT 10;
```

---

## 📚 **Documentation and Knowledge Management**

### **Operational Runbooks**
```markdown
# Operational Runbooks

## Database Connection Issues

### Symptoms
- High response times
- Connection timeout errors
- "Too many connections" errors

### Investigation Steps
1. Check connection pool metrics
2. Review database logs
3. Analyze slow queries
4. Check for connection leaks

### Resolution
1. Restart application pods
2. Increase connection pool size
3. Kill long-running queries
4. Scale database if needed

## High Memory Usage

### Symptoms
- OOMKilled pods
- Slow response times
- High garbage collection activity

### Investigation Steps
1. Check memory metrics
2. Analyze heap dumps
3. Review memory allocation patterns
4. Check for memory leaks

### Resolution
1. Increase pod memory limits
2. Tune JVM parameters
3. Restart affected pods
4. Scale horizontally if needed

## External Service Failures

### Symptoms
- Transaction failures
- Timeout errors
- Circuit breaker activation

### Investigation Steps
1. Check external service status
2. Review API response times
3. Analyze error patterns
4. Test connectivity

### Resolution
1. Implement fallback mechanisms
2. Adjust timeout settings
3. Contact service provider
4. Enable manual processing if needed
```

### **Support Escalation Matrix**
```yaml
Support_Escalation:
  Level_1_Support:
    team: customer_support
    hours: "24/7"
    responsibilities:
      - user_inquiries
      - basic_troubleshooting
      - account_issues
      - transaction_status
    escalation_criteria:
      - technical_issues
      - system_errors
      - security_concerns

  Level_2_Support:
    team: technical_support
    hours: "business_hours"
    responsibilities:
      - technical_troubleshooting
      - integration_issues
      - configuration_problems
      - performance_issues
    escalation_criteria:
      - code_changes_required
      - infrastructure_issues
      - security_incidents

  Level_3_Support:
    team: engineering
    hours: "on_call"
    responsibilities:
      - code_fixes
      - infrastructure_changes
      - security_response
      - architecture_decisions
    escalation_criteria:
      - critical_incidents
      - data_corruption
      - security_breaches
```

**This comprehensive maintenance and support framework ensures reliable, secure, and high-performance operation of the UniversalWallet platform with proactive issue prevention and rapid incident resolution.** 🔧

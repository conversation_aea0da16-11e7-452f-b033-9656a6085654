// UniversalWallet Hub JavaScript
import { gsap } from 'gsap';

// Initialize GSAP
gsap.registerPlugin();

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeHub();
    setupAnimations();
    setupInteractions();
});

// Initialize Hub
function initializeHub() {
    // Hide preloader after content loads
    setTimeout(() => {
        const preloader = document.querySelector('.preloader');
        if (preloader) {
            preloader.classList.add('loaded');
        }
    }, 1000);

    // Set initial theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);
}

// Setup GSAP Animations
function setupAnimations() {
    // Animate hero section
    gsap.timeline()
        .from('.hero-title', {
            duration: 1,
            y: 50,
            opacity: 0,
            ease: 'power3.out',
            delay: 0.5
        })
        .from('.hero-description', {
            duration: 0.8,
            y: 30,
            opacity: 0,
            ease: 'power3.out'
        }, '-=0.5');

    // Animate mockup cards with stagger
    gsap.from('.mockup-card', {
        duration: 0.8,
        y: 60,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 1,
        scrollTrigger: {
            trigger: '.mockups-grid',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
        }
    });

    // Animate design system section
    gsap.from('.design-system-card', {
        duration: 0.6,
        y: 40,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        scrollTrigger: {
            trigger: '.design-system-section',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
        }
    });

    // Animate header on scroll
    gsap.to('.hub-header', {
        scrollTrigger: {
            trigger: 'body',
            start: 'top -50',
            end: 'bottom top',
            toggleClass: {
                targets: '.hub-header',
                className: 'scrolled'
            }
        }
    });
}

// Setup Interactions
function setupInteractions() {
    // Card hover animations
    const cards = document.querySelectorAll('.mockup-card, .design-system-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1.02,
                ease: 'power2.out'
            });
        });

        card.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1,
                ease: 'power2.out'
            });
        });
    });

    // Button hover effects
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.2,
                scale: 1.05,
                ease: 'power2.out'
            });
        });

        button.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.2,
                scale: 1,
                ease: 'power2.out'
            });
        });
    });

    // Smooth scroll for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                gsap.to(window, {
                    duration: 1,
                    scrollTo: target,
                    ease: 'power3.inOut'
                });
            }
        });
    });
}

// Theme Toggle
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
    
    // Animate theme transition
    gsap.to('body', {
        duration: 0.3,
        ease: 'power2.inOut'
    });
}

// Update Theme Icon
function updateThemeIcon(theme) {
    const themeButton = document.querySelector('[onclick="toggleTheme()"] i');
    if (themeButton) {
        themeButton.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// Open in New Tab
function openInNewTab(url) {
    window.open(url, '_blank');
}

// Show Info Modal
function showInfo() {
    const modal = createModal('About UniversalWallet UI Mockups', `
        <div class="modal-content">
            <h3>About This Project</h3>
            <p>These UI mockups demonstrate the comprehensive design system and user interfaces for UniversalWallet, Zimbabwe's premier unified financial platform.</p>
            
            <h4>Features Demonstrated:</h4>
            <ul>
                <li>Modern glassmorphism design patterns</li>
                <li>Responsive design across all devices</li>
                <li>GSAP-powered smooth animations</li>
                <li>Comprehensive component library</li>
                <li>Accessibility-compliant interfaces</li>
            </ul>
            
            <h4>Technology Stack:</h4>
            <ul>
                <li>HTML5 & CSS3 with custom properties</li>
                <li>GSAP for animations</li>
                <li>Modern JavaScript (ES6+)</li>
                <li>Responsive design principles</li>
            </ul>
        </div>
    `);
    
    document.body.appendChild(modal);
    showModal(modal);
}

// Show Technical Details
function showTechnical() {
    const modal = createModal('Technical Implementation Details', `
        <div class="modal-content">
            <h3>Design System Architecture</h3>
            <p>Built with a comprehensive design token system using CSS custom properties for consistent theming and easy maintenance.</p>
            
            <h4>Key Technical Features:</h4>
            <ul>
                <li><strong>Modular CSS:</strong> Component-based architecture</li>
                <li><strong>Design Tokens:</strong> Centralized color, spacing, and typography system</li>
                <li><strong>Glassmorphism:</strong> Modern backdrop-filter effects</li>
                <li><strong>GSAP Integration:</strong> High-performance animations</li>
                <li><strong>Responsive Design:</strong> Mobile-first approach</li>
            </ul>
            
            <h4>Browser Support:</h4>
            <ul>
                <li>Chrome 88+</li>
                <li>Firefox 94+</li>
                <li>Safari 14+</li>
                <li>Edge 88+</li>
            </ul>
            
            <h4>Performance Optimizations:</h4>
            <ul>
                <li>CSS custom properties for efficient theming</li>
                <li>GSAP for hardware-accelerated animations</li>
                <li>Optimized asset loading</li>
                <li>Minimal JavaScript footprint</li>
            </ul>
        </div>
    `);
    
    document.body.appendChild(modal);
    showModal(modal);
}

// Modal System
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal">
            <div class="modal-header">
                <h2>${title}</h2>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    // Add modal styles if not already present
    if (!document.querySelector('#modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'modal-styles';
        styles.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(8px);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: var(--z-modal);
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.3s ease;
            }
            
            .modal-overlay.active {
                opacity: 1;
                pointer-events: all;
            }
            
            .modal {
                background: var(--color-bg-primary);
                border-radius: var(--radius-xl);
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: var(--shadow-2xl);
                transform: scale(0.9);
                transition: transform 0.3s ease;
            }
            
            .modal-overlay.active .modal {
                transform: scale(1);
            }
            
            .modal-header {
                padding: var(--space-6);
                border-bottom: 1px solid var(--color-neutral-200);
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            
            .modal-header h2 {
                margin: 0;
                color: var(--color-text-primary);
            }
            
            .modal-close {
                background: none;
                border: none;
                font-size: var(--font-size-2xl);
                cursor: pointer;
                color: var(--color-text-secondary);
                padding: 0;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: var(--radius-base);
                transition: all var(--transition-base);
            }
            
            .modal-close:hover {
                background: var(--color-neutral-100);
                color: var(--color-text-primary);
            }
            
            .modal-body {
                padding: var(--space-6);
            }
            
            .modal-content h3 {
                color: var(--color-text-primary);
                margin-bottom: var(--space-4);
            }
            
            .modal-content h4 {
                color: var(--color-text-primary);
                margin: var(--space-6) 0 var(--space-3) 0;
            }
            
            .modal-content p {
                color: var(--color-text-secondary);
                line-height: var(--line-height-relaxed);
                margin-bottom: var(--space-4);
            }
            
            .modal-content ul {
                color: var(--color-text-secondary);
                padding-left: var(--space-5);
                margin-bottom: var(--space-4);
            }
            
            .modal-content li {
                margin-bottom: var(--space-2);
                line-height: var(--line-height-relaxed);
            }
        `;
        document.head.appendChild(styles);
    }
    
    return modal;
}

function showModal(modal) {
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
    
    // Close on overlay click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modal.querySelector('.modal-close'));
        }
    });
    
    // Close on escape key
    const escapeHandler = function(e) {
        if (e.key === 'Escape') {
            closeModal(modal.querySelector('.modal-close'));
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);
}

function closeModal(button) {
    const modal = button.closest('.modal-overlay');
    modal.classList.remove('active');
    setTimeout(() => {
        modal.remove();
    }, 300);
}

// Loading states for navigation
function showLoading(element) {
    const originalContent = element.innerHTML;
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    element.disabled = true;
    
    setTimeout(() => {
        element.innerHTML = originalContent;
        element.disabled = false;
    }, 1000);
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('Hub Error:', e.error);
});

// Performance monitoring
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Hub Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
        }, 0);
    });
}

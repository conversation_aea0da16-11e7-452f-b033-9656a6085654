@echo off
REM UniversalWallet UI Test Suite Setup Script for Windows

echo 🚀 Setting up UniversalWallet UI Test Suite...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 16+ first.
    echo Visit: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js detected: 
node --version

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.x first.
    echo Visit: https://python.org/
    pause
    exit /b 1
)

echo ✅ Python detected:
python --version

REM Install npm dependencies
echo 📦 Installing npm dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install npm dependencies
    pause
    exit /b 1
)

echo ✅ npm dependencies installed

REM Install Playwright browsers
echo 🌐 Installing Playwright browsers...
call npx playwright install

if %errorlevel% neq 0 (
    echo ❌ Failed to install Playwright browsers
    pause
    exit /b 1
)

echo ✅ Playwright browsers installed

REM Create test results directory
if not exist "test-results" mkdir test-results
echo ✅ Test results directory created

REM Check if server is running
echo 🔍 Checking if local server is running...
curl -f http://localhost:8001 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Local server is already running on port 8001
) else (
    echo ⚠️  Local server is not running
    echo 📝 To start the server, run:
    echo    cd .. ^&^& python -m http.server 8001
    echo.
    echo 🔧 Or use the npm script:
    echo    npm run start-server
)

echo.
echo 🎉 Setup complete!
echo.
echo 📋 Available commands:
echo    npm test                 - Run all tests
echo    npm run test:mobile      - Run mobile tests
echo    npm run test:desktop     - Run desktop tests
echo    npm run test:animations  - Run animation tests
echo    npm run test:ui          - Run tests with UI mode
echo    npm run test:report      - View test reports
echo.
echo 🚀 To get started:
echo    1. Start the server: npm run start-server
echo    2. Run tests: npm test
echo.
echo 📖 For more information, see README.md

pause

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniversalWallet - Business Dashboard</title>
    <meta name="description" content="Manage your business finances with UniversalWallet's comprehensive business dashboard">
    
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <link rel="stylesheet" href="css/business.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-wallet"></i>
                <span class="logo-text">UniversalWallet</span>
            </div>
            <div class="business-badge">Business</div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-section">
                <div class="menu-title">Overview</div>
                <a href="#dashboard" class="menu-item active" data-page="dashboard">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#accounts" class="menu-item" data-page="accounts">
                    <i class="fas fa-building"></i>
                    <span>Business Accounts</span>
                </a>
                <a href="#analytics" class="menu-item" data-page="analytics">
                    <i class="fas fa-chart-pie"></i>
                    <span>Analytics</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-title">Payments</div>
                <a href="#bulk-payments" class="menu-item" data-page="bulk-payments">
                    <i class="fas fa-layer-group"></i>
                    <span>Bulk Payments</span>
                </a>
                <a href="#invoicing" class="menu-item" data-page="invoicing">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoicing</span>
                </a>
                <a href="#payroll" class="menu-item" data-page="payroll">
                    <i class="fas fa-users-cog"></i>
                    <span>Payroll</span>
                </a>
                <a href="#suppliers" class="menu-item" data-page="suppliers">
                    <i class="fas fa-truck"></i>
                    <span>Supplier Payments</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-title">Management</div>
                <a href="#team" class="menu-item" data-page="team">
                    <i class="fas fa-users"></i>
                    <span>Team Management</span>
                </a>
                <a href="#approvals" class="menu-item" data-page="approvals">
                    <i class="fas fa-check-circle"></i>
                    <span>Approvals</span>
                    <span class="notification-count">3</span>
                </a>
                <a href="#reports" class="menu-item" data-page="reports">
                    <i class="fas fa-file-alt"></i>
                    <span>Reports</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-title">Account</div>
                <a href="#transactions" class="menu-item" data-page="transactions">
                    <i class="fas fa-history"></i>
                    <span>Transactions</span>
                </a>
                <a href="#settings" class="menu-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://images.unsplash.com/photo-**********-0b93528c311a?w=40&h=40&fit=crop&crop=face" alt="Business Owner">
                </div>
                <div class="user-info">
                    <div class="user-name">TechCorp Ltd</div>
                    <div class="user-status">Business Account</div>
                </div>
                <button class="user-menu-toggle">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-sidebar-toggle" id="mobile-sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="../index.html" class="back-to-hub" title="Back to Hub">
                    <i class="fas fa-arrow-left"></i>
                    <span>Hub</span>
                </a>
                <div class="page-title">
                    <h1 id="page-title">Business Dashboard</h1>
                    <p id="page-subtitle">Comprehensive overview of your business finances and operations.</p>
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">5</span>
                    </button>
                    <button class="action-btn" title="Quick Payment">
                        <i class="fas fa-credit-card"></i>
                    </button>
                    <button class="action-btn" title="Export Data">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" title="API Integration">
                        <i class="fas fa-code"></i>
                    </button>
                </div>
                
                <div class="user-menu">
                    <button class="user-menu-btn">
                        <img src="https://images.unsplash.com/photo-**********-0b93528c311a?w=32&h=32&fit=crop&crop=face" alt="User">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <div class="page-content" id="page-content">
            <!-- Dashboard Page -->
            <div class="page" id="dashboard-page" data-page="dashboard">
                <!-- Business Metrics Overview -->
                <section class="business-metrics">
                    <div class="metrics-grid">
                        <div class="metric-card primary">
                            <div class="metric-header">
                                <div class="metric-title">Total Business Balance</div>
                                <button class="metric-toggle">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="metric-value">
                                <span class="currency">$</span>
                                <span class="amount">125,847.50</span>
                            </div>
                            <div class="metric-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+8.2% from last month</span>
                            </div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <div class="metric-title">Monthly Revenue</div>
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="metric-value">
                                <span class="currency">$</span>
                                <span class="amount">45,230.00</span>
                            </div>
                            <div class="metric-subtitle">This month</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <div class="metric-title">Pending Payments</div>
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="metric-value">
                                <span class="currency">$</span>
                                <span class="amount">12,450.00</span>
                            </div>
                            <div class="metric-subtitle">15 transactions</div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-header">
                                <div class="metric-title">Team Members</div>
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-value">
                                <span class="amount">24</span>
                            </div>
                            <div class="metric-subtitle">Active users</div>
                        </div>
                    </div>
                </section>

                <!-- Quick Business Actions -->
                <section class="quick-actions">
                    <div class="section-header">
                        <h2>Quick Actions</h2>
                    </div>
                    <div class="actions-grid">
                        <button class="action-card" data-action="bulk-payment">
                            <div class="action-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Bulk Payment</div>
                                <div class="action-subtitle">Pay multiple suppliers</div>
                            </div>
                        </button>
                        
                        <button class="action-card" data-action="create-invoice">
                            <div class="action-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Create Invoice</div>
                                <div class="action-subtitle">Generate new invoice</div>
                            </div>
                        </button>
                        
                        <button class="action-card" data-action="payroll">
                            <div class="action-icon">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Run Payroll</div>
                                <div class="action-subtitle">Process employee payments</div>
                            </div>
                        </button>
                        
                        <button class="action-card" data-action="expense-report">
                            <div class="action-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Expense Report</div>
                                <div class="action-subtitle">Track business expenses</div>
                            </div>
                        </button>
                        
                        <button class="action-card" data-action="team-invite">
                            <div class="action-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Invite Team</div>
                                <div class="action-subtitle">Add team members</div>
                            </div>
                        </button>
                        
                        <button class="action-card" data-action="api-integration">
                            <div class="action-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">API Integration</div>
                                <div class="action-subtitle">Connect systems</div>
                            </div>
                        </button>
                    </div>
                </section>

                <!-- Business Accounts Overview -->
                <section class="business-accounts">
                    <div class="section-header">
                        <h2>Business Accounts</h2>
                        <button class="btn btn-outline btn-sm">
                            <i class="fas fa-plus"></i>
                            Add Account
                        </button>
                    </div>
                    <div class="accounts-grid">
                        <div class="account-card primary-account">
                            <div class="account-header">
                                <div class="account-provider">
                                    <div class="provider-icon">TC</div>
                                    <div class="provider-info">
                                        <div class="provider-name">TechCorp Business</div>
                                        <div class="account-number">Primary Account</div>
                                    </div>
                                </div>
                                <div class="account-status connected">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                            <div class="account-balance">
                                <div class="balance-label">Available Balance</div>
                                <div class="balance-value">$85,250.00</div>
                            </div>
                            <div class="account-actions">
                                <button class="btn btn-sm btn-primary">Transfer</button>
                                <button class="btn btn-sm btn-secondary">Receive</button>
                                <button class="btn btn-sm btn-outline">Manage</button>
                            </div>
                        </div>
                        
                        <div class="account-card ecocash-business">
                            <div class="account-header">
                                <div class="account-provider">
                                    <div class="provider-icon">EB</div>
                                    <div class="provider-info">
                                        <div class="provider-name">EcoCash Business</div>
                                        <div class="account-number">***8901</div>
                                    </div>
                                </div>
                                <div class="account-status connected">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                            <div class="account-balance">
                                <div class="balance-label">Available Balance</div>
                                <div class="balance-value">$25,450.00</div>
                            </div>
                            <div class="account-actions">
                                <button class="btn btn-sm btn-primary">Transfer</button>
                                <button class="btn btn-sm btn-secondary">Receive</button>
                                <button class="btn btn-sm btn-outline">Manage</button>
                            </div>
                        </div>
                        
                        <div class="account-card bank-business">
                            <div class="account-header">
                                <div class="account-provider">
                                    <div class="provider-icon">ZB</div>
                                    <div class="provider-info">
                                        <div class="provider-name">ZB Business</div>
                                        <div class="account-number">***5678</div>
                                    </div>
                                </div>
                                <div class="account-status connected">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                            <div class="account-balance">
                                <div class="balance-label">Available Balance</div>
                                <div class="balance-value">$15,147.50</div>
                            </div>
                            <div class="account-actions">
                                <button class="btn btn-sm btn-primary">Transfer</button>
                                <button class="btn btn-sm btn-secondary">Receive</button>
                                <button class="btn btn-sm btn-outline">Manage</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Recent Business Activity -->
                <section class="recent-activity">
                    <div class="section-header">
                        <h2>Recent Business Activity</h2>
                        <a href="#transactions" class="view-all-link">View All</a>
                    </div>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon payroll">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-title">Monthly Payroll Processed</div>
                                <div class="activity-subtitle">Today, 10:30 AM • 24 employees</div>
                            </div>
                            <div class="activity-amount negative">-$18,500.00</div>
                            <div class="activity-status completed">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon invoice">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-title">Invoice Payment Received</div>
                                <div class="activity-subtitle">Yesterday, 3:45 PM • Invoice #INV-2024-001</div>
                            </div>
                            <div class="activity-amount positive">+$5,250.00</div>
                            <div class="activity-status completed">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon supplier">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-title">Supplier Payment - Office Supplies</div>
                                <div class="activity-subtitle">Oct 15, 2:20 PM • Supplier ID: SUP-001</div>
                            </div>
                            <div class="activity-amount negative">-$1,250.00</div>
                            <div class="activity-status completed">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon bulk">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-title">Bulk Payment to Contractors</div>
                                <div class="activity-subtitle">Oct 14, 4:15 PM • 8 recipients</div>
                            </div>
                            <div class="activity-amount negative">-$12,800.00</div>
                            <div class="activity-status completed">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Pending Approvals -->
                <section class="pending-approvals">
                    <div class="section-header">
                        <h2>Pending Approvals</h2>
                        <span class="approval-count">3 pending</span>
                    </div>
                    <div class="approvals-list">
                        <div class="approval-item">
                            <div class="approval-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="approval-details">
                                <div class="approval-title">Large Payment Approval Required</div>
                                <div class="approval-subtitle">Payment to ABC Suppliers - $15,000.00</div>
                                <div class="approval-requester">Requested by: John Smith</div>
                            </div>
                            <div class="approval-actions">
                                <button class="btn btn-sm btn-primary">Approve</button>
                                <button class="btn btn-sm btn-outline">Reject</button>
                            </div>
                        </div>
                        
                        <div class="approval-item">
                            <div class="approval-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="approval-details">
                                <div class="approval-title">New Team Member Access</div>
                                <div class="approval-subtitle">Sarah Johnson - Finance Role</div>
                                <div class="approval-requester">Requested by: HR Department</div>
                            </div>
                            <div class="approval-actions">
                                <button class="btn btn-sm btn-primary">Approve</button>
                                <button class="btn btn-sm btn-outline">Reject</button>
                            </div>
                        </div>
                        
                        <div class="approval-item">
                            <div class="approval-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="approval-details">
                                <div class="approval-title">API Integration Request</div>
                                <div class="approval-subtitle">Connect to Accounting System</div>
                                <div class="approval-requester">Requested by: IT Department</div>
                            </div>
                            <div class="approval-actions">
                                <button class="btn btn-sm btn-primary">Approve</button>
                                <button class="btn btn-sm btn-outline">Reject</button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="js/business.js"></script>
</body>
</html>

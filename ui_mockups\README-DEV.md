# UniversalWallet UI Mockups - Modern Development Environment

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18+ (recommended: use [nvm](https://github.com/nvm-sh/nvm))
- **npm** or **yarn** or **pnpm**

### Installation & Setup

1. **Navigate to the project directory:**
   ```bash
   cd ui_mockups
   ```

2. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start development server:**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser:**
   - The server will automatically open http://localhost:8000
   - Hot reload is enabled - changes will reflect immediately

## 📜 Available Scripts

### Development
- `npm run dev` - Start development server with hot reload
- `npm run preview` - Preview production build locally

### Build & Production
- `npm run build` - Build for production
- `npm run serve` - Serve production build

### Code Quality
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## 🛠️ Modern Development Features

### ✅ **Hot Module Replacement (HMR)**
- Instant updates without page refresh
- CSS changes apply immediately
- JavaScript changes reload automatically

### ✅ **ES6 Modules**
- Modern import/export syntax
- Tree shaking for smaller bundles
- Better dependency management

### ✅ **Code Quality Tools**
- **ESLint** for code linting
- **Prettier** for code formatting
- **PostCSS** with Autoprefixer

### ✅ **Build Optimization**
- **Vite** for lightning-fast builds
- **Terser** for JavaScript minification
- **Legacy browser support**
- **Source maps** for debugging

### ✅ **Asset Management**
- Automatic asset optimization
- Font loading optimization
- Image compression
- CSS preprocessing with Sass support

## 🏗️ Project Structure

```
ui_mockups/
├── assets/                 # Shared assets
│   ├── css/               # Global styles
│   ├── js/                # Shared JavaScript
│   └── images/            # Images and icons
├── individual/            # Individual dashboard
├── business/              # Business dashboard
├── marketing/             # Marketing website
├── mobile/                # Mobile interface
├── admin/                 # Admin dashboard
├── design-system/         # Design system docs
├── dist/                  # Production build (generated)
├── node_modules/          # Dependencies (generated)
├── package.json           # Project configuration
├── vite.config.js         # Vite configuration
├── .eslintrc.json         # ESLint configuration
├── .prettierrc            # Prettier configuration
└── postcss.config.js      # PostCSS configuration
```

## 🌐 Available Mockups

Once the dev server is running, access these URLs:

- **🏠 Hub:** http://localhost:8000
- **👤 Individual Dashboard:** http://localhost:8000/individual/
- **🏢 Business Dashboard:** http://localhost:8000/business/
- **📱 Marketing Website:** http://localhost:8000/marketing/
- **📱 Mobile Interface:** http://localhost:8000/mobile/
- **⚙️ Admin Dashboard:** http://localhost:8000/admin/
- **🎨 Design System:** http://localhost:8000/design-system/

## 🔧 Configuration

### Vite Configuration
The `vite.config.js` file includes:
- Multi-page application setup
- Development server configuration
- Build optimization settings
- Legacy browser support
- Asset handling rules

### ESLint Configuration
The `.eslintrc.json` includes:
- Modern JavaScript rules
- Browser environment setup
- GSAP globals configuration
- Code quality standards

### Prettier Configuration
The `.prettierrc` includes:
- Consistent code formatting
- Single quotes preference
- 2-space indentation
- Line length limits

## 🚀 Production Build

### Build for Production
```bash
npm run build
```

This creates a `dist/` folder with:
- ✅ Minified and optimized assets
- ✅ Vendor chunk splitting
- ✅ Legacy browser compatibility
- ✅ Source maps for debugging
- ✅ Asset fingerprinting for caching

### Preview Production Build
```bash
npm run preview
```

## 🔍 Development Tips

### Hot Reload
- CSS changes apply instantly
- JavaScript changes trigger automatic reload
- HTML changes refresh the page
- Asset changes update automatically

### Debugging
- Source maps are enabled in development
- Console errors show original file locations
- Network tab shows actual file requests
- Vue DevTools compatible (if using Vue)

### Performance
- Vite uses esbuild for fast transpilation
- Dependencies are pre-bundled
- Only changed modules are reloaded
- Optimized for development speed

## 🐛 Troubleshooting

### Port Already in Use
If port 8000 is busy, Vite will automatically try the next available port.

### Module Not Found
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Build Errors
```bash
# Check for linting errors
npm run lint

# Format code
npm run format
```

### GSAP Issues
GSAP is now imported as an ES6 module. If you see GSAP errors:
1. Ensure `npm install` was run
2. Check that imports are correct in JavaScript files
3. Verify Vite configuration includes GSAP optimization

## 📦 Dependencies

### Production Dependencies
- **gsap** - Animation library

### Development Dependencies
- **vite** - Build tool and dev server
- **eslint** - Code linting
- **prettier** - Code formatting
- **@vitejs/plugin-legacy** - Legacy browser support
- **autoprefixer** - CSS vendor prefixes
- **postcss** - CSS processing
- **sass** - CSS preprocessing
- **terser** - JavaScript minification

## 🎯 Next Steps

1. **Start developing:** Run `npm run dev` and begin coding
2. **Code quality:** Use `npm run lint` and `npm run format` regularly
3. **Testing:** Add your preferred testing framework
4. **Deployment:** Use `npm run build` for production builds
5. **CI/CD:** Integrate with your preferred CI/CD pipeline

---

**Happy coding! 🎉**

The modern development environment is now ready for professional development with hot reload, code quality tools, and optimized builds.

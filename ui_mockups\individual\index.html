<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniversalWallet - Personal Dashboard</title>
    <meta name="description" content="Manage your personal finances with UniversalWallet's unified dashboard">
    
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <link rel="stylesheet" href="css/individual.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-wallet"></i>
                <span class="logo-text">UniversalWallet</span>
            </div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-section">
                <div class="menu-title">Main</div>
                <a href="#dashboard" class="menu-item active" data-page="dashboard">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#accounts" class="menu-item" data-page="accounts">
                    <i class="fas fa-credit-card"></i>
                    <span>My Accounts</span>
                </a>
                <a href="#transfers" class="menu-item" data-page="transfers">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Transfers</span>
                </a>
                <a href="#bills" class="menu-item" data-page="bills">
                    <i class="fas fa-receipt"></i>
                    <span>Bill Payments</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-title">Services</div>
                <a href="#groups" class="menu-item" data-page="groups">
                    <i class="fas fa-users"></i>
                    <span>Savings Groups</span>
                </a>
                <a href="#cards" class="menu-item" data-page="cards">
                    <i class="fas fa-credit-card"></i>
                    <span>Virtual Cards</span>
                </a>
                <a href="#analytics" class="menu-item" data-page="analytics">
                    <i class="fas fa-chart-pie"></i>
                    <span>Analytics</span>
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-title">Account</div>
                <a href="#transactions" class="menu-item" data-page="transactions">
                    <i class="fas fa-history"></i>
                    <span>Transactions</span>
                </a>
                <a href="#settings" class="menu-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="https://images.unsplash.com/photo-*************-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="User Avatar">
                </div>
                <div class="user-info">
                    <div class="user-name">John Doe</div>
                    <div class="user-status">Personal Account</div>
                </div>
                <button class="user-menu-toggle">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-sidebar-toggle" id="mobile-sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <a href="../index.html" class="back-to-hub" title="Back to Hub">
                    <i class="fas fa-arrow-left"></i>
                    <span>Hub</span>
                </a>
                <div class="page-title">
                    <h1 id="page-title">Dashboard</h1>
                    <p id="page-subtitle">Welcome back, John! Here's your financial overview.</p>
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="action-btn" title="Quick Transfer">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button class="action-btn" title="QR Scanner">
                        <i class="fas fa-qrcode"></i>
                    </button>
                </div>
                
                <div class="user-menu">
                    <button class="user-menu-btn">
                        <img src="https://images.unsplash.com/photo-*************-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="User">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <div class="page-content" id="page-content">
            <!-- Dashboard Page -->
            <div class="page" id="dashboard-page" data-page="dashboard">
                <!-- Balance Overview -->
                <section class="balance-overview">
                    <div class="balance-cards">
                        <div class="balance-card primary">
                            <div class="balance-header">
                                <div class="balance-title">Total Balance</div>
                                <button class="balance-toggle">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="balance-amount">
                                <span class="currency">$</span>
                                <span class="amount">2,847.50</span>
                            </div>
                            <div class="balance-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+12.5% from last month</span>
                            </div>
                        </div>
                        
                        <div class="balance-card">
                            <div class="balance-header">
                                <div class="balance-title">Available Cash</div>
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="balance-amount">
                                <span class="currency">$</span>
                                <span class="amount">2,347.50</span>
                            </div>
                            <div class="balance-subtitle">Across all accounts</div>
                        </div>
                        
                        <div class="balance-card">
                            <div class="balance-header">
                                <div class="balance-title">Savings Groups</div>
                                <i class="fas fa-piggy-bank"></i>
                            </div>
                            <div class="balance-amount">
                                <span class="currency">$</span>
                                <span class="amount">500.00</span>
                            </div>
                            <div class="balance-subtitle">3 active groups</div>
                        </div>
                    </div>
                </section>

                <!-- Quick Actions -->
                <section class="quick-actions">
                    <div class="section-header">
                        <h2>Quick Actions</h2>
                    </div>
                    <div class="actions-grid">
                        <button class="action-card" data-action="send-money">
                            <div class="action-icon">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Send Money</div>
                                <div class="action-subtitle">Transfer to contacts</div>
                            </div>
                        </button>

                        <button class="action-card" data-action="pay-bills">
                            <div class="action-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Pay Bills</div>
                                <div class="action-subtitle">Utilities & services</div>
                            </div>
                        </button>

                        <button class="action-card" data-action="payment-request">
                            <div class="action-icon">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Payment Request</div>
                                <div class="action-subtitle">Request from contacts</div>
                            </div>
                        </button>

                        <button class="action-card" data-action="top-up">
                            <div class="action-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Top Up</div>
                                <div class="action-subtitle">Add money</div>
                            </div>
                        </button>

                        <button class="action-card" data-action="scan-qr">
                            <div class="action-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">Scan QR</div>
                                <div class="action-subtitle">Quick payments</div>
                            </div>
                        </button>

                        <button class="action-card" data-action="history">
                            <div class="action-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">History</div>
                                <div class="action-subtitle">View transactions</div>
                            </div>
                        </button>
                    </div>
                </section>

                <!-- My Wallets -->
                <section class="my-wallets">
                    <div class="section-header">
                        <h2>My Wallets</h2>
                        <a href="#wallets" class="view-all-link">View All</a>
                    </div>
                    <div class="wallets-grid">
                        <div class="wallet-card ecocash">
                            <div class="wallet-header">
                                <div class="wallet-provider">
                                    <div class="provider-icon">EC</div>
                                    <div class="provider-info">
                                        <div class="provider-name">EcoCash Wallet</div>
                                        <div class="wallet-status connected">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Connected</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="wallet-balance">
                                <div class="balance-label">Available Balance</div>
                                <div class="balance-amounts">
                                    <div class="balance-value">ZWL 5,325.75</div>
                                    <div class="balance-separator">|</div>
                                    <div class="balance-value">USD 150.25</div>
                                </div>
                            </div>
                            <div class="wallet-actions">
                                <button class="btn btn-sm btn-primary">Send</button>
                                <button class="btn btn-sm btn-secondary">Receive</button>
                            </div>
                        </div>

                        <div class="wallet-card onemoney">
                            <div class="wallet-header">
                                <div class="wallet-provider">
                                    <div class="provider-icon">OM</div>
                                    <div class="provider-info">
                                        <div class="provider-name">OneMoney Wallet</div>
                                        <div class="wallet-status connected">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Connected</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="wallet-balance">
                                <div class="balance-label">Available Balance</div>
                                <div class="balance-amounts">
                                    <div class="balance-value">ZWL 2,150.5</div>
                                    <div class="balance-separator">|</div>
                                    <div class="balance-value">USD 75.5</div>
                                </div>
                            </div>
                            <div class="wallet-actions">
                                <button class="btn btn-sm btn-primary">Send</button>
                                <button class="btn btn-sm btn-secondary">Receive</button>
                            </div>
                        </div>

                        <div class="wallet-card bank">
                            <div class="wallet-header">
                                <div class="wallet-provider">
                                    <div class="provider-icon">ZB</div>
                                    <div class="provider-info">
                                        <div class="provider-name">ZB Bank Wallet</div>
                                        <div class="wallet-status connected">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Connected</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="wallet-balance">
                                <div class="balance-label">Available Balance</div>
                                <div class="balance-amounts">
                                    <div class="balance-value">ZWL 15,250</div>
                                    <div class="balance-separator">|</div>
                                    <div class="balance-value">USD 500</div>
                                </div>
                            </div>
                            <div class="wallet-actions">
                                <button class="btn btn-sm btn-primary">Send</button>
                                <button class="btn btn-sm btn-secondary">Receive</button>
                            </div>
                        </div>

                        <div class="wallet-card add-wallet">
                            <div class="add-wallet-content">
                                <div class="add-wallet-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="add-wallet-text">
                                    <div class="add-wallet-title">Add Wallet</div>
                                    <div class="add-wallet-subtitle">Connect new account</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Recent Transactions -->
                <section class="recent-transactions">
                    <div class="section-header">
                        <h2>Recent Transactions</h2>
                        <a href="#transactions" class="view-all-link">View All</a>
                    </div>
                    <div class="transactions-list">
                        <div class="transaction-item">
                            <div class="transaction-icon received">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-title">Payment from Jane Smith</div>
                                <div class="transaction-subtitle">Today, 2:30 PM</div>
                            </div>
                            <div class="transaction-amount positive">+USD 150.00</div>
                            <div class="transaction-status completed">
                                <span class="status-text">completed</span>
                            </div>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-icon sent">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-title">Coffee payment to Bob's Cafe</div>
                                <div class="transaction-subtitle">Yesterday, 9:15 AM</div>
                            </div>
                            <div class="transaction-amount negative">-USD 50.00</div>
                            <div class="transaction-status completed">
                                <span class="status-text">completed</span>
                            </div>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-icon refund">
                                <i class="fas fa-undo"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-title">Refund from Amazon</div>
                                <div class="transaction-subtitle">Oct 15, 4:20 PM</div>
                            </div>
                            <div class="transaction-amount positive">+USD 75.00</div>
                            <div class="transaction-status pending">
                                <span class="status-text">pending</span>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script type="module" src="js/individual.js"></script>
</body>
</html>

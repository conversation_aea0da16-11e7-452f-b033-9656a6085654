#!/usr/bin/env node

/**
 * UniversalWallet UI Test Runner
 * Comprehensive test execution script with reporting and analysis
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      suites: {},
      issues: []
    };
    
    this.config = {
      baseURL: 'http://localhost:8000',
      browsers: ['chromium', 'firefox', 'webkit'],
      devices: ['Desktop Chrome', 'Mobile Chrome', 'Mobile Safari', 'iPad'],
      outputDir: './test-results',
      reportFormats: ['html', 'json', 'junit']
    };
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check if server is running
    try {
      const response = await fetch(this.config.baseURL);
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}`);
      }
      console.log('✅ Local server is running');
    } catch (error) {
      console.error('❌ Local server is not running');
      console.log('Please start the server with: python -m http.server 8001');
      process.exit(1);
    }
    
    // Check if Playwright is installed
    try {
      execSync('npx playwright --version', { stdio: 'pipe' });
      console.log('✅ Playwright is installed');
    } catch (error) {
      console.error('❌ Playwright is not installed');
      console.log('Please install with: npm install');
      process.exit(1);
    }
    
    // Ensure output directory exists
    if (!fs.existsSync(this.config.outputDir)) {
      fs.mkdirSync(this.config.outputDir, { recursive: true });
    }
  }

  async runTestSuite(suiteName, options = {}) {
    console.log(`\n🧪 Running ${suiteName} tests...`);
    
    const startTime = Date.now();
    let command = 'npx playwright test';
    
    // Add grep pattern for specific test types
    if (options.grep) {
      command += ` --grep "${options.grep}"`;
    }
    
    // Add specific spec file
    if (options.spec) {
      command += ` specs/${options.spec}`;
    }
    
    // Add browser filter
    if (options.browser) {
      command += ` --project="${options.browser}"`;
    }
    
    // Add reporter
    command += ' --reporter=json';
    
    try {
      const output = execSync(command, { 
        stdio: 'pipe',
        encoding: 'utf8',
        cwd: __dirname
      });
      
      const duration = Date.now() - startTime;
      console.log(`✅ ${suiteName} completed in ${duration}ms`);
      
      return this.parseTestResults(output, suiteName, duration);
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ ${suiteName} failed in ${duration}ms`);
      
      return this.parseTestResults(error.stdout || error.message, suiteName, duration, true);
    }
  }

  parseTestResults(output, suiteName, duration, failed = false) {
    try {
      const results = JSON.parse(output);
      
      const suiteResults = {
        name: suiteName,
        duration,
        tests: results.tests || [],
        passed: 0,
        failed: 0,
        skipped: 0,
        issues: []
      };
      
      // Count test results
      if (results.tests) {
        results.tests.forEach(test => {
          switch (test.outcome) {
            case 'passed':
              suiteResults.passed++;
              break;
            case 'failed':
              suiteResults.failed++;
              suiteResults.issues.push({
                test: test.title,
                error: test.error?.message || 'Unknown error',
                location: test.location
              });
              break;
            case 'skipped':
              suiteResults.skipped++;
              break;
          }
        });
      }
      
      this.testResults.suites[suiteName] = suiteResults;
      this.testResults.total += suiteResults.passed + suiteResults.failed + suiteResults.skipped;
      this.testResults.passed += suiteResults.passed;
      this.testResults.failed += suiteResults.failed;
      this.testResults.skipped += suiteResults.skipped;
      this.testResults.duration += duration;
      this.testResults.issues.push(...suiteResults.issues);
      
      return suiteResults;
    } catch (error) {
      console.warn(`Warning: Could not parse test results for ${suiteName}`);
      return {
        name: suiteName,
        duration,
        tests: [],
        passed: 0,
        failed: failed ? 1 : 0,
        skipped: 0,
        issues: failed ? [{ test: suiteName, error: 'Parse error', location: null }] : []
      };
    }
  }

  async runAllTests() {
    console.log('🚀 Starting UniversalWallet UI Test Suite\n');
    
    await this.checkPrerequisites();
    
    // Test suites configuration
    const testSuites = [
      {
        name: 'Hub Page',
        spec: 'hub.test.js',
        grep: '@smoke'
      },
      {
        name: 'Marketing Website',
        spec: 'marketing.test.js',
        grep: '@desktop'
      },
      {
        name: 'Individual Dashboard',
        spec: 'individual.test.js',
        grep: '@desktop'
      },
      {
        name: 'Business Dashboard',
        spec: 'business.test.js',
        grep: '@desktop'
      },
      {
        name: 'Mobile Interface',
        spec: 'mobile.test.js',
        grep: '@mobile'
      },
      {
        name: 'Animations & Performance',
        spec: 'animations.test.js',
        grep: '@animations'
      },
      {
        name: 'Cross-Browser Compatibility',
        spec: 'cross-browser.test.js',
        grep: '@cross-browser'
      }
    ];
    
    // Run each test suite
    for (const suite of testSuites) {
      await this.runTestSuite(suite.name, suite);
    }
    
    // Generate reports
    await this.generateReports();
    
    // Display summary
    this.displaySummary();
    
    return this.testResults;
  }

  async runSpecificTests(type) {
    console.log(`🎯 Running ${type} tests only\n`);
    
    await this.checkPrerequisites();
    
    const testConfigs = {
      smoke: { grep: '@smoke', description: 'Smoke Tests' },
      mobile: { grep: '@mobile', description: 'Mobile Tests' },
      desktop: { grep: '@desktop', description: 'Desktop Tests' },
      animations: { grep: '@animations', description: 'Animation Tests' },
      forms: { grep: '@forms', description: 'Form Tests' },
      navigation: { grep: '@navigation', description: 'Navigation Tests' },
      responsive: { grep: '@responsive', description: 'Responsive Tests' },
      accessibility: { grep: '@accessibility', description: 'Accessibility Tests' },
      performance: { grep: '@performance', description: 'Performance Tests' }
    };
    
    const config = testConfigs[type];
    if (!config) {
      console.error(`❌ Unknown test type: ${type}`);
      console.log(`Available types: ${Object.keys(testConfigs).join(', ')}`);
      process.exit(1);
    }
    
    await this.runTestSuite(config.description, config);
    await this.generateReports();
    this.displaySummary();
    
    return this.testResults;
  }

  async generateReports() {
    console.log('\n📊 Generating test reports...');
    
    // Generate JSON report
    const jsonReport = {
      summary: {
        total: this.testResults.total,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        skipped: this.testResults.skipped,
        duration: this.testResults.duration,
        passRate: this.testResults.total > 0 ? (this.testResults.passed / this.testResults.total * 100).toFixed(2) : 0
      },
      suites: this.testResults.suites,
      issues: this.testResults.issues,
      timestamp: new Date().toISOString(),
      environment: {
        baseURL: this.config.baseURL,
        browsers: this.config.browsers,
        devices: this.config.devices
      }
    };
    
    fs.writeFileSync(
      path.join(this.config.outputDir, 'test-report.json'),
      JSON.stringify(jsonReport, null, 2)
    );
    
    // Generate HTML summary report
    const htmlReport = this.generateHTMLReport(jsonReport);
    fs.writeFileSync(
      path.join(this.config.outputDir, 'test-summary.html'),
      htmlReport
    );
    
    // Generate issues report
    if (this.testResults.issues.length > 0) {
      const issuesReport = this.generateIssuesReport();
      fs.writeFileSync(
        path.join(this.config.outputDir, 'issues-report.md'),
        issuesReport
      );
    }
    
    console.log(`✅ Reports generated in ${this.config.outputDir}/`);
  }

  generateHTMLReport(data) {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>UniversalWallet UI Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .suite { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .issue { background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>UniversalWallet UI Test Report</h1>
    
    <div class="summary">
        <h2>Test Summary</h2>
        <p><strong>Total Tests:</strong> ${data.summary.total}</p>
        <p><strong>Passed:</strong> <span class="passed">${data.summary.passed}</span></p>
        <p><strong>Failed:</strong> <span class="failed">${data.summary.failed}</span></p>
        <p><strong>Skipped:</strong> <span class="skipped">${data.summary.skipped}</span></p>
        <p><strong>Pass Rate:</strong> ${data.summary.passRate}%</p>
        <p><strong>Duration:</strong> ${(data.summary.duration / 1000).toFixed(2)}s</p>
        <p><strong>Generated:</strong> ${data.timestamp}</p>
    </div>
    
    <h2>Test Suites</h2>
    ${Object.values(data.suites).map(suite => `
        <div class="suite">
            <h3>${suite.name}</h3>
            <p>Passed: <span class="passed">${suite.passed}</span> | 
               Failed: <span class="failed">${suite.failed}</span> | 
               Skipped: <span class="skipped">${suite.skipped}</span> | 
               Duration: ${(suite.duration / 1000).toFixed(2)}s</p>
        </div>
    `).join('')}
    
    ${data.issues.length > 0 ? `
        <h2>Issues Found</h2>
        ${data.issues.map(issue => `
            <div class="issue">
                <strong>${issue.test}</strong><br>
                ${issue.error}
            </div>
        `).join('')}
    ` : '<h2>No Issues Found ✅</h2>'}
    
</body>
</html>`;
  }

  generateIssuesReport() {
    let report = '# UniversalWallet UI Test Issues Report\n\n';
    report += `Generated: ${new Date().toISOString()}\n\n`;
    
    if (this.testResults.issues.length === 0) {
      report += '## ✅ No Issues Found\n\nAll tests passed successfully!\n';
      return report;
    }
    
    report += `## Summary\n\n`;
    report += `- Total Issues: ${this.testResults.issues.length}\n`;
    report += `- Failed Tests: ${this.testResults.failed}\n`;
    report += `- Pass Rate: ${(this.testResults.passed / this.testResults.total * 100).toFixed(2)}%\n\n`;
    
    report += '## Issues by Category\n\n';
    
    const issuesByCategory = {};
    this.testResults.issues.forEach(issue => {
      const category = this.categorizeIssue(issue);
      if (!issuesByCategory[category]) {
        issuesByCategory[category] = [];
      }
      issuesByCategory[category].push(issue);
    });
    
    Object.entries(issuesByCategory).forEach(([category, issues]) => {
      report += `### ${category}\n\n`;
      issues.forEach(issue => {
        report += `- **${issue.test}**\n`;
        report += `  - Error: ${issue.error}\n`;
        if (issue.location) {
          report += `  - Location: ${issue.location}\n`;
        }
        report += '\n';
      });
    });
    
    report += '## Recommendations\n\n';
    report += this.generateRecommendations();
    
    return report;
  }

  categorizeIssue(issue) {
    const error = issue.error.toLowerCase();
    
    if (error.includes('timeout') || error.includes('waiting')) {
      return 'Performance & Timing Issues';
    } else if (error.includes('element') || error.includes('locator')) {
      return 'Element Location Issues';
    } else if (error.includes('animation') || error.includes('gsap')) {
      return 'Animation Issues';
    } else if (error.includes('responsive') || error.includes('viewport')) {
      return 'Responsive Design Issues';
    } else if (error.includes('accessibility') || error.includes('aria')) {
      return 'Accessibility Issues';
    } else if (error.includes('javascript') || error.includes('script')) {
      return 'JavaScript Issues';
    } else {
      return 'General Issues';
    }
  }

  generateRecommendations() {
    let recommendations = '';
    
    if (this.testResults.failed > 0) {
      recommendations += '1. **Fix Failed Tests**: Address the specific issues listed above\n';
    }
    
    if (this.testResults.issues.some(i => i.error.includes('timeout'))) {
      recommendations += '2. **Performance**: Consider optimizing animations and loading times\n';
    }
    
    if (this.testResults.issues.some(i => i.error.includes('element'))) {
      recommendations += '3. **Element Stability**: Ensure elements are stable before interaction\n';
    }
    
    recommendations += '4. **Cross-Browser Testing**: Test across all supported browsers\n';
    recommendations += '5. **Mobile Testing**: Verify mobile interactions work correctly\n';
    recommendations += '6. **Accessibility**: Ensure all interactive elements are accessible\n';
    
    return recommendations;
  }

  displaySummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 TEST EXECUTION SUMMARY');
    console.log('='.repeat(60));
    
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`⏭️  Skipped: ${this.testResults.skipped}`);
    console.log(`⏱️  Duration: ${(this.testResults.duration / 1000).toFixed(2)}s`);
    
    const passRate = this.testResults.total > 0 ? 
      (this.testResults.passed / this.testResults.total * 100).toFixed(2) : 0;
    console.log(`📊 Pass Rate: ${passRate}%`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ ISSUES FOUND:');
      this.testResults.issues.slice(0, 5).forEach(issue => {
        console.log(`  • ${issue.test}: ${issue.error}`);
      });
      
      if (this.testResults.issues.length > 5) {
        console.log(`  ... and ${this.testResults.issues.length - 5} more issues`);
      }
      
      console.log(`\n📄 Full report: ${this.config.outputDir}/issues-report.md`);
    } else {
      console.log('\n🎉 ALL TESTS PASSED!');
    }
    
    console.log(`\n📊 Detailed report: ${this.config.outputDir}/test-summary.html`);
    console.log('='.repeat(60));
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const testRunner = new TestRunner();
  
  if (args.length === 0) {
    // Run all tests
    await testRunner.runAllTests();
  } else {
    const testType = args[0];
    await testRunner.runSpecificTests(testType);
  }
}

// Export for programmatic use
module.exports = TestRunner;

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test execution failed:', error.message);
    process.exit(1);
  });
}

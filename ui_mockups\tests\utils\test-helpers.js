// Test Helper Functions for UniversalWallet UI Tests

/**
 * Wait for animations to complete
 * @param {import('@playwright/test').Page} page 
 * @param {number} timeout - Timeout in milliseconds
 */
async function waitForAnimations(page, timeout = 3000) {
  // Wait for GSAP animations to complete
  await page.waitForFunction(() => {
    return window.gsap && window.gsap.globalTimeline.getChildren().length === 0;
  }, { timeout });
  
  // Additional wait for CSS transitions
  await page.waitForTimeout(500);
}

/**
 * Check if element has glassmorphism effects
 * @param {import('@playwright/test').Locator} element 
 */
async function hasGlassmorphism(element) {
  const backdropFilter = await element.evaluate(el => 
    window.getComputedStyle(el).backdropFilter
  );
  return backdropFilter && backdropFilter !== 'none';
}

/**
 * Simulate mobile touch interaction
 * @param {import('@playwright/test').Page} page 
 * @param {string} selector 
 */
async function mobileTouch(page, selector) {
  const element = page.locator(selector);
  const box = await element.boundingBox();
  
  if (box) {
    await page.touchscreen.tap(box.x + box.width / 2, box.y + box.height / 2);
  }
}

/**
 * Check if animations are smooth (60fps)
 * @param {import('@playwright/test').Page} page 
 * @param {Function} action - Action that triggers animation
 */
async function checkAnimationPerformance(page, action) {
  let frameCount = 0;
  let startTime = Date.now();
  
  // Monitor frame rate during animation
  await page.evaluate(() => {
    window.frameCount = 0;
    function countFrames() {
      window.frameCount++;
      requestAnimationFrame(countFrames);
    }
    countFrames();
  });
  
  await action();
  await waitForAnimations(page);
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  frameCount = await page.evaluate(() => window.frameCount);
  
  const fps = (frameCount / duration) * 1000;
  return fps;
}

/**
 * Test responsive behavior across viewports
 * @param {import('@playwright/test').Page} page 
 * @param {Array} viewports - Array of viewport sizes
 * @param {Function} testFunction - Test to run at each viewport
 */
async function testResponsive(page, viewports, testFunction) {
  const results = [];
  
  for (const viewport of viewports) {
    await page.setViewportSize(viewport);
    await page.waitForTimeout(500); // Allow layout to settle
    
    try {
      await testFunction(page, viewport);
      results.push({ viewport, success: true });
    } catch (error) {
      results.push({ viewport, success: false, error: error.message });
    }
  }
  
  return results;
}

/**
 * Check if element is visible in viewport
 * @param {import('@playwright/test').Locator} element 
 */
async function isInViewport(element) {
  return await element.evaluate(el => {
    const rect = el.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  });
}

/**
 * Wait for element to be stable (not moving)
 * @param {import('@playwright/test').Locator} element 
 * @param {number} timeout 
 */
async function waitForStable(element, timeout = 2000) {
  let previousBox = await element.boundingBox();
  let stableCount = 0;
  const requiredStableFrames = 5;
  
  const startTime = Date.now();
  
  while (stableCount < requiredStableFrames && Date.now() - startTime < timeout) {
    await new Promise(resolve => setTimeout(resolve, 100));
    const currentBox = await element.boundingBox();
    
    if (previousBox && currentBox &&
        Math.abs(previousBox.x - currentBox.x) < 1 &&
        Math.abs(previousBox.y - currentBox.y) < 1) {
      stableCount++;
    } else {
      stableCount = 0;
    }
    
    previousBox = currentBox;
  }
}

/**
 * Test form validation
 * @param {import('@playwright/test').Page} page 
 * @param {string} formSelector 
 * @param {Object} testData 
 */
async function testFormValidation(page, formSelector, testData) {
  const form = page.locator(formSelector);
  
  // Test empty form submission
  const submitButton = form.locator('button[type="submit"], .btn-primary').first();
  await submitButton.click();
  
  // Check for validation messages
  const validationMessages = await form.locator('.error, .invalid, [aria-invalid="true"]').count();
  
  // Test with invalid data
  for (const [field, data] of Object.entries(testData.invalid || {})) {
    const input = form.locator(`[name="${field}"], #${field}`);
    if (await input.count() > 0) {
      await input.fill(data.value);
      await input.blur();
      
      // Check for field-specific validation
      const fieldError = await input.locator('~ .error, + .error').count();
      if (fieldError === 0) {
        // Check for form-level validation
        await submitButton.click();
      }
    }
  }
  
  // Test with valid data
  for (const [field, data] of Object.entries(testData.valid || {})) {
    const input = form.locator(`[name="${field}"], #${field}`);
    if (await input.count() > 0) {
      await input.fill(data.value);
    }
  }
  
  return {
    hasValidation: validationMessages > 0,
    formSelector,
    fieldsCount: await form.locator('input, select, textarea').count()
  };
}

/**
 * Check accessibility features
 * @param {import('@playwright/test').Page} page 
 */
async function checkAccessibility(page) {
  // Check for ARIA labels
  const ariaElements = await page.locator('[aria-label], [aria-labelledby], [role]').count();
  
  // Check for keyboard navigation
  const focusableElements = await page.locator(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  ).count();
  
  // Check color contrast (simplified)
  const contrastIssues = await page.evaluate(() => {
    const elements = document.querySelectorAll('*');
    let issues = 0;
    
    elements.forEach(el => {
      const style = window.getComputedStyle(el);
      const bgColor = style.backgroundColor;
      const textColor = style.color;
      
      // Simple contrast check (would need more sophisticated implementation)
      if (bgColor === textColor) {
        issues++;
      }
    });
    
    return issues;
  });
  
  return {
    ariaElements,
    focusableElements,
    contrastIssues
  };
}

/**
 * Test loading states
 * @param {import('@playwright/test').Page} page 
 * @param {string} triggerSelector 
 */
async function testLoadingState(page, triggerSelector) {
  const trigger = page.locator(triggerSelector);
  
  // Capture initial state
  const initialContent = await trigger.innerHTML();
  
  // Trigger action
  await trigger.click();
  
  // Check for loading indicators
  const hasSpinner = await page.locator('.fa-spinner, .loading, [class*="spin"]').count() > 0;
  const hasLoadingText = await page.locator(':text("Loading"), :text("Please wait")').count() > 0;
  
  // Wait for loading to complete
  await page.waitForTimeout(2000);
  
  // Check if content changed back
  const finalContent = await trigger.innerHTML();
  
  return {
    hasLoadingIndicator: hasSpinner || hasLoadingText,
    contentChanged: initialContent !== finalContent,
    initialContent,
    finalContent
  };
}

module.exports = {
  waitForAnimations,
  hasGlassmorphism,
  mobileTouch,
  checkAnimationPerformance,
  testResponsive,
  isInViewport,
  waitForStable,
  testFormValidation,
  checkAccessibility,
  testLoadingState
};

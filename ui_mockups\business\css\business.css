/* Business Dashboard Styles */

/* Import Individual Dashboard Base Styles */
@import url('../../individual/css/individual.css');

/* Business-specific overrides and additions */

/* Business Badge */
.business-badge {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: var(--space-2);
}

/* Notification Count */
.notification-count {
  background: var(--color-error);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

/* Business Metrics */
.business-metrics {
  margin-bottom: var(--space-12);
}

.metrics-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-6);
}

.metric-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-secondary), var(--color-primary));
}

.metric-card.primary {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
  color: white;
  border-color: var(--color-secondary);
}

.metric-card.primary::before {
  background: rgba(255, 255, 255, 0.3);
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.metric-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: inherit;
  opacity: 0.8;
}

.metric-card.primary .metric-title {
  color: white;
}

.metric-toggle {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
  opacity: 0.7;
}

.metric-toggle:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.metric-header i {
  font-size: var(--font-size-lg);
  color: var(--color-secondary);
}

.metric-card.primary .metric-header i {
  color: white;
}

.metric-value {
  display: flex;
  align-items: baseline;
  gap: var(--space-1);
  margin-bottom: var(--space-3);
}

.metric-value .currency {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  opacity: 0.8;
}

.metric-value .amount {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.metric-change.positive {
  color: var(--color-success);
}

.metric-card.primary .metric-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.metric-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  margin-top: var(--space-2);
}

/* Business Account Cards */
.account-card.primary-account {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: white;
  border-color: var(--color-primary);
}

.account-card.primary-account::before {
  background: rgba(255, 255, 255, 0.3);
}

.account-card.primary-account .provider-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.account-card.primary-account .provider-name,
.account-card.primary-account .balance-label,
.account-card.primary-account .balance-value {
  color: white;
}

.account-card.primary-account .account-number {
  color: rgba(255, 255, 255, 0.8);
}

.account-card.ecocash-business::before {
  background: linear-gradient(90deg, #FF6B35, #F7931E);
}

.account-card.ecocash-business .provider-icon {
  background: linear-gradient(135deg, #FF6B35, #F7931E);
}

.account-card.bank-business::before {
  background: linear-gradient(90deg, #059669, #10B981);
}

.account-card.bank-business .provider-icon {
  background: linear-gradient(135deg, #059669, #10B981);
}

/* Business Activity */
.recent-activity {
  margin-bottom: var(--space-12);
}

.activity-list {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
  transition: all var(--transition-base);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background: var(--glass-bg-strong);
}

.activity-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.activity-icon.payroll {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
}

.activity-icon.invoice {
  background: linear-gradient(135deg, var(--color-success), var(--color-success-dark));
}

.activity-icon.supplier {
  background: linear-gradient(135deg, var(--color-warning), var(--color-warning-dark));
}

.activity-icon.bulk {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
}

.activity-details {
  flex: 1;
}

.activity-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.activity-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.activity-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-right: var(--space-4);
}

.activity-amount.positive {
  color: var(--color-success);
}

.activity-amount.negative {
  color: var(--color-text-primary);
}

.activity-status {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.activity-status.completed {
  background: var(--color-success);
  color: white;
}

.activity-status.pending {
  background: var(--color-warning);
  color: white;
}

.activity-status.failed {
  background: var(--color-error);
  color: white;
}

/* Pending Approvals */
.pending-approvals {
  margin-bottom: var(--space-12);
}

.approval-count {
  background: var(--color-error);
  color: white;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.approvals-list {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.approval-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
  transition: all var(--transition-base);
}

.approval-item:last-child {
  border-bottom: none;
}

.approval-item:hover {
  background: var(--glass-bg-strong);
}

.approval-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-warning), var(--color-warning-dark));
  color: white;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.approval-details {
  flex: 1;
}

.approval-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.approval-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-1);
}

.approval-requester {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
  font-style: italic;
}

.approval-actions {
  display: flex;
  gap: var(--space-2);
}

/* Business-specific responsive adjustments */
@media (max-width: 1024px) {
  .metrics-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .approval-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .approval-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .activity-item {
    flex-wrap: wrap;
    gap: var(--space-3);
  }
  
  .activity-details {
    order: 1;
    flex: 1 1 100%;
  }
  
  .activity-amount {
    order: 2;
    margin-right: 0;
  }
  
  .activity-status {
    order: 3;
  }
}

@media (max-width: 480px) {
  .metric-value .amount {
    font-size: var(--font-size-3xl);
  }
  
  .approval-actions {
    flex-direction: column;
  }
  
  .approval-actions .btn {
    width: 100%;
  }
}

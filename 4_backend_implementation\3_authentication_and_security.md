# 3. Authentication and Security Implementation

## 🔐 **Security Implementation Overview**

This document provides comprehensive implementation guidelines for authentication, authorization, security controls, and fraud prevention mechanisms in the UniversalWallet platform, ensuring enterprise-level security and regulatory compliance.

## 🔑 **JWT Authentication Implementation**

### **JWT Token Provider**
```java
@Component
@Slf4j
public class JwtTokenProvider {
    
    @Value("${app.jwt.secret}")
    private String jwtSecret;
    
    @Value("${app.jwt.access-token-expiration}")
    private int accessTokenExpiration;
    
    @Value("${app.jwt.refresh-token-expiration}")
    private int refreshTokenExpiration;
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    @PostConstruct
    protected void init() {
        jwtSecret = Base64.getEncoder().encodeToString(jwtSecret.getBytes());
    }
    
    public String generateAccessToken(User user) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + accessTokenExpiration);
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("user_id", user.getUserId().toString());
        claims.put("user_type", user.getUserType().name());
        claims.put("kyc_level", user.getKycLevel().name());
        claims.put("permissions", getUserPermissions(user));
        claims.put("token_type", "access");
        
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(user.getPhoneNumber())
            .setIssuedAt(now)
            .setExpiration(expiryDate)
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
    }
    
    public String generateRefreshToken(User user) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshTokenExpiration);
        
        String refreshToken = Jwts.builder()
            .setSubject(user.getUserId().toString())
            .setIssuedAt(now)
            .setExpiration(expiryDate)
            .claim("token_type", "refresh")
            .signWith(SignatureAlgorithm.HS512, jwtSecret)
            .compact();
        
        // Store refresh token in Redis with expiration
        String key = "refresh_token:" + user.getUserId();
        redisTemplate.opsForValue().set(key, refreshToken, 
            Duration.ofMillis(refreshTokenExpiration));
        
        return refreshToken;
    }
    
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            
            // Check if token is blacklisted
            String tokenId = getTokenId(token);
            Boolean isBlacklisted = (Boolean) redisTemplate.opsForValue()
                .get("blacklisted_token:" + tokenId);
            
            return !Boolean.TRUE.equals(isBlacklisted);
            
        } catch (JwtException | IllegalArgumentException e) {
            log.error("Invalid JWT token: {}", e.getMessage());
            return false;
        }
    }
    
    public String refreshAccessToken(String refreshToken) {
        if (!validateToken(refreshToken)) {
            throw new InvalidTokenException("Invalid refresh token");
        }
        
        Claims claims = getClaimsFromToken(refreshToken);
        String userId = claims.getSubject();
        
        // Verify refresh token exists in Redis
        String key = "refresh_token:" + userId;
        String storedToken = (String) redisTemplate.opsForValue().get(key);
        
        if (!refreshToken.equals(storedToken)) {
            throw new InvalidTokenException("Refresh token not found or expired");
        }
        
        // Get user and generate new access token
        User user = userRepository.findById(UUID.fromString(userId))
            .orElseThrow(() -> new UserNotFoundException("User not found"));
        
        return generateAccessToken(user);
    }
    
    public void blacklistToken(String token) {
        String tokenId = getTokenId(token);
        Claims claims = getClaimsFromToken(token);
        
        // Calculate remaining time until expiration
        long expirationTime = claims.getExpiration().getTime() - System.currentTimeMillis();
        
        if (expirationTime > 0) {
            redisTemplate.opsForValue().set("blacklisted_token:" + tokenId, true, 
                Duration.ofMillis(expirationTime));
        }
    }
    
    private List<String> getUserPermissions(User user) {
        List<String> permissions = new ArrayList<>();
        
        switch (user.getUserType()) {
            case PERSONAL:
                permissions.addAll(Arrays.asList(
                    "account:read", "account:link", "transaction:create", 
                    "transaction:read", "bill:pay", "profile:update"
                ));
                if (user.getKycLevel() == KycLevel.ENHANCED) {
                    permissions.addAll(Arrays.asList(
                        "savings:create", "loan:apply", "card:request"
                    ));
                }
                break;
                
            case BUSINESS:
                permissions.addAll(Arrays.asList(
                    "business:manage", "bulk_payment:create", "invoice:manage", 
                    "api:access", "reports:generate", "team:manage"
                ));
                break;
                
            case AGENT:
                permissions.addAll(Arrays.asList(
                    "cash_in:process", "cash_out:process", "user:onboard", 
                    "commission:view", "float:manage"
                ));
                break;
                
            case ADMIN:
                permissions.addAll(Arrays.asList(
                    "system:manage", "user:manage", "transaction:investigate", 
                    "compliance:monitor", "reports:admin"
                ));
                break;
        }
        
        return permissions;
    }
}
```

### **JWT Request Filter**
```java
@Component
@Slf4j
public class JwtRequestFilter extends OncePerRequestFilter {
    
    private final JwtTokenProvider jwtTokenProvider;
    private final UserDetailsService userDetailsService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                   HttpServletResponse response, 
                                   FilterChain chain) throws ServletException, IOException {
        
        final String requestTokenHeader = request.getHeader("Authorization");
        
        String phoneNumber = null;
        String jwtToken = null;
        
        if (requestTokenHeader != null && requestTokenHeader.startsWith("Bearer ")) {
            jwtToken = requestTokenHeader.substring(7);
            try {
                phoneNumber = jwtTokenProvider.getPhoneNumberFromToken(jwtToken);
            } catch (IllegalArgumentException e) {
                log.error("Unable to get JWT Token");
            } catch (ExpiredJwtException e) {
                log.error("JWT Token has expired");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("{\"error\":\"Token expired\"}");
                return;
            }
        }
        
        if (phoneNumber != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            
            UserDetails userDetails = userDetailsService.loadUserByUsername(phoneNumber);
            
            if (jwtTokenProvider.validateToken(jwtToken)) {
                
                // Check for concurrent sessions
                if (checkConcurrentSessions(userDetails, jwtToken)) {
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    response.getWriter().write("{\"error\":\"Session limit exceeded\"}");
                    return;
                }
                
                // Create authentication token
                UsernamePasswordAuthenticationToken authToken = 
                    new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);
                
                // Update last activity
                updateLastActivity(userDetails);
            }
        }
        
        chain.doFilter(request, response);
    }
    
    private boolean checkConcurrentSessions(UserDetails userDetails, String token) {
        String userId = ((UserPrincipal) userDetails).getUserId().toString();
        String sessionKey = "active_sessions:" + userId;
        
        Set<String> activeSessions = redisTemplate.opsForSet().members(sessionKey);
        
        if (activeSessions != null && activeSessions.size() >= 3) { // Max 3 concurrent sessions
            // Remove expired tokens
            activeSessions.removeIf(sessionToken -> !jwtTokenProvider.validateToken(sessionToken));
            
            if (activeSessions.size() >= 3 && !activeSessions.contains(token)) {
                return true; // Session limit exceeded
            }
        }
        
        // Add current token to active sessions
        redisTemplate.opsForSet().add(sessionKey, token);
        redisTemplate.expire(sessionKey, Duration.ofHours(24));
        
        return false;
    }
    
    private void updateLastActivity(UserDetails userDetails) {
        String userId = ((UserPrincipal) userDetails).getUserId().toString();
        String activityKey = "last_activity:" + userId;
        redisTemplate.opsForValue().set(activityKey, LocalDateTime.now(), Duration.ofHours(24));
    }
}
```

---

## 🛡️ **Fraud Detection Implementation**

### **Fraud Detection Service**
```java
@Service
@Slf4j
public class FraudDetectionServiceImpl implements FraudDetectionService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final UserRepository userRepository;
    private final TransactionRepository transactionRepository;
    private final MachineLearningService mlService;
    
    @Override
    public FraudAssessment assessTransaction(UUID userId, TransferRequest request) {
        log.debug("Assessing fraud risk for user: {} transaction amount: {}", userId, request.getAmount());
        
        FraudAssessment assessment = FraudAssessment.builder()
            .userId(userId)
            .transactionAmount(request.getAmount())
            .riskFactors(new HashMap<>())
            .build();
        
        // Calculate individual risk factors
        double velocityRisk = calculateVelocityRisk(userId, request.getAmount());
        double amountRisk = calculateAmountRisk(userId, request.getAmount());
        double locationRisk = calculateLocationRisk(userId, request.getDeviceInfo());
        double behaviorRisk = calculateBehaviorRisk(userId, request);
        double deviceRisk = calculateDeviceRisk(userId, request.getDeviceInfo());
        
        // Store risk factors
        assessment.getRiskFactors().put("velocity", velocityRisk);
        assessment.getRiskFactors().put("amount", amountRisk);
        assessment.getRiskFactors().put("location", locationRisk);
        assessment.getRiskFactors().put("behavior", behaviorRisk);
        assessment.getRiskFactors().put("device", deviceRisk);
        
        // Calculate weighted risk score
        double riskScore = calculateWeightedRiskScore(assessment.getRiskFactors());
        assessment.setRiskScore(riskScore);
        
        // Determine risk level
        if (riskScore >= 90) {
            assessment.setRiskLevel(RiskLevel.CRITICAL);
            assessment.setRecommendedAction(FraudAction.BLOCK_TRANSACTION);
        } else if (riskScore >= 70) {
            assessment.setRiskLevel(RiskLevel.HIGH);
            assessment.setRecommendedAction(FraudAction.MANUAL_REVIEW);
        } else if (riskScore >= 40) {
            assessment.setRiskLevel(RiskLevel.MEDIUM);
            assessment.setRecommendedAction(FraudAction.ADDITIONAL_VERIFICATION);
        } else {
            assessment.setRiskLevel(RiskLevel.LOW);
            assessment.setRecommendedAction(FraudAction.ALLOW);
        }
        
        // Log high-risk transactions
        if (riskScore >= 70) {
            log.warn("High-risk transaction detected - User: {}, Risk Score: {}, Factors: {}", 
                userId, riskScore, assessment.getRiskFactors());
        }
        
        // Store assessment for ML training
        storeAssessmentForTraining(assessment);
        
        return assessment;
    }
    
    private double calculateVelocityRisk(UUID userId, BigDecimal amount) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneHourAgo = now.minusHours(1);
        LocalDateTime oneDayAgo = now.minusDays(1);
        
        // Get recent transaction counts and amounts
        long transactionsLastHour = transactionRepository.countUserTransactionsInPeriod(
            userId, oneHourAgo, now);
        long transactionsLastDay = transactionRepository.countUserTransactionsInPeriod(
            userId, oneDayAgo, now);
        
        BigDecimal amountLastHour = transactionRepository.sumUserTransactionAmountInPeriod(
            userId, oneHourAgo, now);
        BigDecimal amountLastDay = transactionRepository.sumUserTransactionAmountInPeriod(
            userId, oneDayAgo, now);
        
        // Calculate velocity risk based on frequency and amount
        double frequencyRisk = 0;
        if (transactionsLastHour > 5) frequencyRisk += 30;
        if (transactionsLastDay > 20) frequencyRisk += 20;
        
        double amountRisk = 0;
        if (amountLastHour.compareTo(new BigDecimal("10000")) > 0) amountRisk += 25;
        if (amountLastDay.compareTo(new BigDecimal("50000")) > 0) amountRisk += 25;
        
        return Math.min(100, frequencyRisk + amountRisk);
    }
    
    private double calculateAmountRisk(UUID userId, BigDecimal amount) {
        // Get user's transaction history for pattern analysis
        List<BigDecimal> recentAmounts = transactionRepository.getRecentTransactionAmounts(
            userId, LocalDateTime.now().minusDays(30), 50);
        
        if (recentAmounts.isEmpty()) {
            // New user - higher risk for large amounts
            if (amount.compareTo(new BigDecimal("1000")) > 0) {
                return 60;
            }
            return 20;
        }
        
        // Calculate statistical measures
        BigDecimal avgAmount = recentAmounts.stream()
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(new BigDecimal(recentAmounts.size()), RoundingMode.HALF_UP);
        
        BigDecimal maxAmount = recentAmounts.stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        
        // Risk based on deviation from normal pattern
        double deviationFromAvg = amount.subtract(avgAmount).abs()
            .divide(avgAmount.max(BigDecimal.ONE), RoundingMode.HALF_UP).doubleValue();
        
        double deviationFromMax = amount.compareTo(maxAmount) > 0 ? 
            amount.subtract(maxAmount).divide(maxAmount.max(BigDecimal.ONE), RoundingMode.HALF_UP).doubleValue() : 0;
        
        return Math.min(100, (deviationFromAvg * 30) + (deviationFromMax * 40));
    }
    
    private double calculateLocationRisk(UUID userId, DeviceInfo deviceInfo) {
        if (deviceInfo == null || deviceInfo.getLocation() == null) {
            return 30; // Unknown location is medium risk
        }
        
        // Get user's recent locations
        List<Location> recentLocations = getUserRecentLocations(userId, 30);
        
        if (recentLocations.isEmpty()) {
            return 20; // New user location
        }
        
        Location currentLocation = deviceInfo.getLocation();
        
        // Check if current location is within normal range
        boolean isNormalLocation = recentLocations.stream()
            .anyMatch(loc -> calculateDistance(currentLocation, loc) < 50); // 50km radius
        
        if (!isNormalLocation) {
            // Check if it's a completely new country/region
            boolean isDifferentCountry = recentLocations.stream()
                .noneMatch(loc -> loc.getCountry().equals(currentLocation.getCountry()));
            
            return isDifferentCountry ? 70 : 40;
        }
        
        return 10; // Normal location
    }
    
    private double calculateBehaviorRisk(UUID userId, TransferRequest request) {
        // Analyze behavioral patterns
        double riskScore = 0;
        
        // Check for unusual recipient patterns
        boolean isNewRecipient = !transactionRepository.hasTransactionToRecipient(
            userId, request.getRecipientPhone());
        
        if (isNewRecipient) {
            riskScore += 15;
        }
        
        // Check transaction timing patterns
        LocalTime currentTime = LocalTime.now();
        List<LocalTime> normalTransactionTimes = getUserNormalTransactionTimes(userId);
        
        boolean isUnusualTime = normalTransactionTimes.stream()
            .noneMatch(time -> Math.abs(Duration.between(time, currentTime).toMinutes()) < 120);
        
        if (isUnusualTime && !normalTransactionTimes.isEmpty()) {
            riskScore += 20;
        }
        
        // Check for round number amounts (potential fraud indicator)
        if (isRoundAmount(request.getAmount())) {
            riskScore += 10;
        }
        
        return Math.min(100, riskScore);
    }
    
    private double calculateDeviceRisk(UUID userId, DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return 50; // Unknown device is high risk
        }
        
        // Check if device is known
        boolean isKnownDevice = isDeviceKnown(userId, deviceInfo.getDeviceId());
        
        if (!isKnownDevice) {
            // New device - check device characteristics
            double newDeviceRisk = 40;
            
            // Check for suspicious device characteristics
            if (deviceInfo.isRooted() || deviceInfo.isJailbroken()) {
                newDeviceRisk += 30;
            }
            
            if (deviceInfo.hasVpn()) {
                newDeviceRisk += 20;
            }
            
            if (deviceInfo.isEmulator()) {
                newDeviceRisk += 40;
            }
            
            return Math.min(100, newDeviceRisk);
        }
        
        return 5; // Known device is low risk
    }
    
    private double calculateWeightedRiskScore(Map<String, Double> riskFactors) {
        // Weighted scoring based on factor importance
        double velocityWeight = 0.25;
        double amountWeight = 0.20;
        double locationWeight = 0.20;
        double behaviorWeight = 0.20;
        double deviceWeight = 0.15;
        
        return (riskFactors.get("velocity") * velocityWeight) +
               (riskFactors.get("amount") * amountWeight) +
               (riskFactors.get("location") * locationWeight) +
               (riskFactors.get("behavior") * behaviorWeight) +
               (riskFactors.get("device") * deviceWeight);
    }
}
```

---

## 🔒 **Encryption Service Implementation**

### **Data Encryption Service**
```java
@Service
@Slf4j
public class EncryptionServiceImpl implements EncryptionService {
    
    @Value("${app.encryption.key}")
    private String encryptionKey;
    
    @Value("${app.encryption.algorithm}")
    private String algorithm = "AES/GCM/NoPadding";
    
    private SecretKeySpec secretKey;
    private final SecureRandom secureRandom = new SecureRandom();
    
    @PostConstruct
    public void init() {
        try {
            MessageDigest sha = MessageDigest.getInstance("SHA-256");
            byte[] key = sha.digest(encryptionKey.getBytes(StandardCharsets.UTF_8));
            secretKey = new SecretKeySpec(key, "AES");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to initialize encryption service", e);
        }
    }
    
    @Override
    public String encrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            
            // Generate random IV
            byte[] iv = new byte[12]; // GCM standard IV size
            secureRandom.nextBytes(iv);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);
            
            byte[] encryptedData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // Combine IV and encrypted data
            byte[] encryptedWithIv = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
            System.arraycopy(encryptedData, 0, encryptedWithIv, iv.length, encryptedData.length);
            
            return Base64.getEncoder().encodeToString(encryptedWithIv);
            
        } catch (Exception e) {
            log.error("Encryption failed", e);
            throw new EncryptionException("Failed to encrypt data", e);
        }
    }
    
    @Override
    public String decrypt(String encryptedText) {
        try {
            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);
            
            // Extract IV and encrypted data
            byte[] iv = new byte[12];
            byte[] encryptedData = new byte[encryptedWithIv.length - 12];
            
            System.arraycopy(encryptedWithIv, 0, iv, 0, 12);
            System.arraycopy(encryptedWithIv, 12, encryptedData, 0, encryptedData.length);
            
            Cipher cipher = Cipher.getInstance(algorithm);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);
            
            byte[] decryptedData = cipher.doFinal(encryptedData);
            
            return new String(decryptedData, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            log.error("Decryption failed", e);
            throw new DecryptionException("Failed to decrypt data", e);
        }
    }
    
    @Override
    public String hashPassword(String password) {
        return BCrypt.hashpw(password, BCrypt.gensalt(12));
    }
    
    @Override
    public boolean verifyPassword(String password, String hashedPassword) {
        return BCrypt.checkpw(password, hashedPassword);
    }
    
    @Override
    public String generateSecureToken() {
        byte[] tokenBytes = new byte[32];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }
}
```

**This comprehensive authentication and security implementation provides enterprise-level protection with multi-layered security controls, fraud detection, and regulatory compliance for the UniversalWallet platform.** 🔐

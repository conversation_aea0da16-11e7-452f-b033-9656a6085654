# UniversalWallet UI Test Suite

Comprehensive Playwright test suite for the UniversalWallet UI mockups, covering all interactive elements, user flows, animations, and cross-browser compatibility.

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ installed
- Python 3.x for local server
- Modern browsers (Chrome, Firefox, Safari)

### Installation
```bash
cd ui_mockups/tests
npm install
npx playwright install
```

### Start Local Server
```bash
# In the ui_mockups directory
python -m http.server 8001
```

### Run Tests
```bash
# Run all tests
npm test

# Run specific test types
npm run test:mobile
npm run test:desktop
npm run test:animations

# Run with UI mode
npm run test:ui

# Run in headed mode (see browser)
npm run test:headed
```

## 📋 Test Coverage

### 🏠 Hub Page Tests (`hub.test.js`)
- ✅ Page loading and basic functionality
- ✅ Theme toggle functionality
- ✅ Mockup card navigation
- ✅ Modal interactions
- ✅ Responsive behavior
- ✅ Accessibility compliance

### 🌐 Marketing Website Tests (`marketing.test.js`)
- ✅ Hero section and animations
- ✅ Navigation and smooth scrolling
- ✅ Feature showcase
- ✅ Pricing section
- ✅ Phone mockup interactions
- ✅ CTA button functionality
- ✅ Footer and form handling

### 👤 Individual Dashboard Tests (`individual.test.js`)
- ✅ Sidebar navigation and collapse
- ✅ Balance overview and toggle
- ✅ Quick action buttons
- ✅ Connected accounts display
- ✅ Transaction history
- ✅ Real-time updates
- ✅ Mobile responsive behavior

### 🏢 Business Dashboard Tests (`business.test.js`)
- ✅ Business-specific navigation
- ✅ Business metrics display
- ✅ Approval workflow testing
- ✅ Team management features
- ✅ Bulk payment actions
- ✅ Business account management
- ✅ Notification system

### 📱 Mobile Interface Tests (`mobile.test.js`)
- ✅ Mobile app container
- ✅ Status bar and time display
- ✅ Touch interactions
- ✅ Bottom navigation
- ✅ Notification overlay
- ✅ Swipe gestures
- ✅ Mobile-specific styling

### 🎬 Animation Tests (`animations.test.js`)
- ✅ GSAP animation loading
- ✅ Scroll-triggered animations
- ✅ Hover effects and transitions
- ✅ Page entrance animations
- ✅ Performance testing (60fps)
- ✅ Counter animations
- ✅ Parallax effects

### 🌍 Cross-Browser Tests (`cross-browser.test.js`)
- ✅ CSS Grid and Flexbox support
- ✅ Custom properties support
- ✅ Backdrop-filter support
- ✅ ES6+ JavaScript features
- ✅ Responsive design across viewports
- ✅ Touch and mobile interactions
- ✅ Accessibility compliance
- ✅ Performance across browsers

## 🎯 Test Categories

### Smoke Tests (`@smoke`)
Essential functionality tests that must pass for basic operation.

### Desktop Tests (`@desktop`)
Tests specifically for desktop browser interactions and layouts.

### Mobile Tests (`@mobile`)
Tests for mobile devices, touch interactions, and responsive behavior.

### Animation Tests (`@animations`)
Performance and functionality tests for GSAP animations and transitions.

### Form Tests (`@forms`)
Input validation, form submission, and user input handling.

### Navigation Tests (`@navigation`)
Menu navigation, page transitions, and routing functionality.

### Responsive Tests (`@responsive`)
Layout adaptation across different screen sizes and orientations.

### Accessibility Tests (`@accessibility`)
WCAG compliance, keyboard navigation, and screen reader support.

### Performance Tests (`@performance`)
Loading times, animation performance, and resource usage.

## 🛠 Test Configuration

### Browsers Tested
- **Chromium** (Chrome/Edge)
- **Firefox**
- **WebKit** (Safari)

### Devices Tested
- **Desktop**: 1920x1080, 1366x768
- **Mobile**: iPhone 12, Pixel 5
- **Tablet**: iPad Pro

### Viewports Tested
- Mobile Small: 320x568
- Mobile Medium: 375x667
- Mobile Large: 414x896
- Tablet Portrait: 768x1024
- Tablet Landscape: 1024x768
- Desktop Small: 1366x768
- Desktop Large: 1920x1080

## 📊 Test Reports

### Generated Reports
- **HTML Report**: `test-results/html-report/index.html`
- **JSON Report**: `test-results/test-report.json`
- **JUnit XML**: `test-results/junit.xml`
- **Issues Report**: `test-results/issues-report.md`
- **Summary Report**: `test-results/test-summary.html`

### Viewing Reports
```bash
# Open HTML report
npm run test:report

# View in browser
open test-results/test-summary.html
```

## 🔧 Advanced Usage

### Custom Test Runner
```bash
# Run custom test runner
node run-tests.js

# Run specific test types
node run-tests.js smoke
node run-tests.js mobile
node run-tests.js animations
```

### Environment Variables
```bash
# Custom base URL
BASE_URL=http://localhost:3000 npm test

# Specific browser
BROWSER=firefox npm test

# Debug mode
DEBUG=1 npm test
```

### Test Debugging
```bash
# Debug specific test
npm run test:debug -- --grep "should load hub page"

# Run single test file
npx playwright test specs/hub.test.js --headed

# Record test actions
npx playwright codegen localhost:8001
```

## 🐛 Common Issues and Solutions

### Server Not Running
```bash
Error: connect ECONNREFUSED 127.0.0.1:8001
Solution: Start the local server with `python -m http.server 8001`
```

### Playwright Not Installed
```bash
Error: Executable doesn't exist
Solution: Run `npx playwright install`
```

### Animation Timeouts
```bash
Error: Timeout waiting for animations
Solution: Increase timeout in playwright.config.js or check GSAP loading
```

### Mobile Touch Issues
```bash
Error: Touch events not working
Solution: Ensure proper viewport meta tags and touch event handlers
```

## 📈 Performance Benchmarks

### Expected Performance
- **Page Load**: < 3 seconds
- **Animation FPS**: > 30 fps (target 60 fps)
- **Interaction Response**: < 100ms
- **Memory Usage**: < 50MB increase per page

### Performance Monitoring
```bash
# Run performance tests only
npm test -- --grep "@performance"

# Monitor memory usage
npm test -- --grep "memory"

# Check animation performance
npm test -- --grep "60fps"
```

## 🔍 Test Development

### Adding New Tests
1. Create test file in `specs/` directory
2. Follow naming convention: `feature.test.js`
3. Use appropriate tags: `@desktop`, `@mobile`, etc.
4. Include helper functions from `utils/test-helpers.js`

### Test Structure
```javascript
test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/feature/');
    await waitForAnimations(page);
  });

  test('should do something @desktop @smoke', async ({ page }) => {
    // Test implementation
  });
});
```

### Helper Functions
- `waitForAnimations(page)` - Wait for GSAP animations
- `hasGlassmorphism(element)` - Check backdrop-filter effects
- `testLoadingState(page, selector)` - Test loading indicators
- `checkAccessibility(page)` - Verify accessibility features
- `testResponsive(page, viewports, testFn)` - Test across viewports

## 🎯 Quality Gates

### Minimum Requirements
- **Pass Rate**: > 95%
- **Performance**: All pages load < 3s
- **Accessibility**: WCAG 2.1 AA compliance
- **Cross-Browser**: Works in Chrome, Firefox, Safari
- **Mobile**: Touch interactions work correctly

### CI/CD Integration
```yaml
# Example GitHub Actions
- name: Run UI Tests
  run: |
    npm install
    npx playwright install
    python -m http.server 8001 &
    npm test
    kill %1
```

## 📞 Support

### Getting Help
- Check the issues report for common problems
- Review test logs in `test-results/`
- Use debug mode for detailed output
- Consult Playwright documentation

### Contributing
1. Add tests for new features
2. Ensure tests pass across all browsers
3. Update documentation
4. Follow existing patterns and conventions

---

**Built for UniversalWallet UI Mockups** | **Powered by Playwright** | **Quality Assured** ✨

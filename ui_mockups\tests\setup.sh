#!/bin/bash

# UniversalWallet UI Test Suite Setup Script

echo "🚀 Setting up UniversalWallet UI Test Suite..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version 16+ required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if Python is installed
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "❌ Python is not installed. Please install Python 3.x first."
    echo "Visit: https://python.org/"
    exit 1
fi

# Determine Python command
PYTHON_CMD="python"
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
fi

echo "✅ Python detected: $($PYTHON_CMD --version)"

# Install npm dependencies
echo "📦 Installing npm dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install npm dependencies"
    exit 1
fi

echo "✅ npm dependencies installed"

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
npx playwright install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Playwright browsers"
    exit 1
fi

echo "✅ Playwright browsers installed"

# Create test results directory
mkdir -p test-results
echo "✅ Test results directory created"

# Check if server is running
echo "🔍 Checking if local server is running..."
if curl -f http://localhost:8001 &> /dev/null; then
    echo "✅ Local server is already running on port 8001"
else
    echo "⚠️  Local server is not running"
    echo "📝 To start the server, run:"
    echo "   cd .. && $PYTHON_CMD -m http.server 8001"
    echo ""
    echo "🔧 Or use the npm script:"
    echo "   npm run start-server"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Available commands:"
echo "   npm test                 - Run all tests"
echo "   npm run test:mobile      - Run mobile tests"
echo "   npm run test:desktop     - Run desktop tests"
echo "   npm run test:animations  - Run animation tests"
echo "   npm run test:ui          - Run tests with UI mode"
echo "   npm run test:report      - View test reports"
echo ""
echo "🚀 To get started:"
echo "   1. Start the server: npm run start-server"
echo "   2. Run tests: npm test"
echo ""
echo "📖 For more information, see README.md"

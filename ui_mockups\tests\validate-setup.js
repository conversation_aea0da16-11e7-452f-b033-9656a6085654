#!/usr/bin/env node

/**
 * UniversalWallet UI Test Setup Validator
 * Validates that all prerequisites are met for running the test suite
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SetupValidator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      issues: []
    };
  }

  log(message, type = 'info') {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    
    console.log(`${icons[type]} ${message}`);
  }

  check(description, testFn) {
    try {
      const result = testFn();
      if (result === true) {
        this.log(description, 'success');
        this.results.passed++;
      } else if (result === 'warning') {
        this.log(`${description} (with warnings)`, 'warning');
        this.results.warnings++;
      } else {
        this.log(`${description} - ${result}`, 'error');
        this.results.failed++;
        this.results.issues.push({ description, error: result });
      }
    } catch (error) {
      this.log(`${description} - ${error.message}`, 'error');
      this.results.failed++;
      this.results.issues.push({ description, error: error.message });
    }
  }

  async checkNodeJS() {
    this.check('Node.js installation', () => {
      try {
        const version = execSync('node --version', { encoding: 'utf8' }).trim();
        const majorVersion = parseInt(version.slice(1).split('.')[0]);
        
        if (majorVersion >= 16) {
          return true;
        } else {
          return `Node.js ${version} found, but version 16+ required`;
        }
      } catch (error) {
        return 'Node.js not found';
      }
    });
  }

  async checkPython() {
    this.check('Python installation', () => {
      try {
        let version;
        try {
          version = execSync('python --version', { encoding: 'utf8' }).trim();
        } catch {
          version = execSync('python3 --version', { encoding: 'utf8' }).trim();
        }
        
        if (version.includes('Python 3.')) {
          return true;
        } else {
          return `${version} found, but Python 3.x required`;
        }
      } catch (error) {
        return 'Python not found';
      }
    });
  }

  async checkNpmDependencies() {
    this.check('npm dependencies', () => {
      if (!fs.existsSync('package.json')) {
        return 'package.json not found';
      }
      
      if (!fs.existsSync('node_modules')) {
        return 'node_modules not found - run npm install';
      }
      
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const requiredDeps = ['@playwright/test'];
      
      for (const dep of requiredDeps) {
        if (!packageJson.devDependencies || !packageJson.devDependencies[dep]) {
          return `Missing dependency: ${dep}`;
        }
      }
      
      return true;
    });
  }

  async checkPlaywright() {
    this.check('Playwright installation', () => {
      try {
        const version = execSync('npx playwright --version', { encoding: 'utf8' }).trim();
        return true;
      } catch (error) {
        return 'Playwright not installed - run npx playwright install';
      }
    });
  }

  async checkTestFiles() {
    this.check('Test files structure', () => {
      const requiredFiles = [
        'specs/hub.test.js',
        'specs/marketing.test.js',
        'specs/individual.test.js',
        'specs/business.test.js',
        'specs/mobile.test.js',
        'specs/animations.test.js',
        'specs/cross-browser.test.js',
        'utils/test-helpers.js',
        'playwright.config.js'
      ];
      
      const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
      
      if (missingFiles.length > 0) {
        return `Missing files: ${missingFiles.join(', ')}`;
      }
      
      return true;
    });
  }

  async checkServer() {
    this.check('Local server availability', async () => {
      try {
        const response = await fetch('http://localhost:8000');
        if (response.ok) {
          return true;
        } else {
          return `Server returned status ${response.status}`;
        }
      } catch (error) {
        return 'Server not running on port 8000 - start with: python -m http.server 8000';
      }
    });
  }

  async checkMockupFiles() {
    this.check('UI mockup files', () => {
      const requiredMockups = [
        '../index.html',
        '../marketing/index.html',
        '../individual/index.html',
        '../business/index.html',
        '../mobile/index.html'
      ];
      
      const missingMockups = requiredMockups.filter(file => !fs.existsSync(file));
      
      if (missingMockups.length > 0) {
        return `Missing mockup files: ${missingMockups.join(', ')}`;
      }
      
      return true;
    });
  }

  async checkOutputDirectory() {
    this.check('Test output directory', () => {
      if (!fs.existsSync('test-results')) {
        try {
          fs.mkdirSync('test-results', { recursive: true });
          return true;
        } catch (error) {
          return 'Cannot create test-results directory';
        }
      }
      return true;
    });
  }

  async checkBrowsers() {
    this.check('Browser availability', () => {
      try {
        // Check if browsers are installed
        execSync('npx playwright install --dry-run', { stdio: 'pipe' });
        return true;
      } catch (error) {
        return 'warning'; // Browsers might need installation
      }
    });
  }

  async validateConfiguration() {
    this.check('Playwright configuration', () => {
      if (!fs.existsSync('playwright.config.js')) {
        return 'playwright.config.js not found';
      }
      
      const config = fs.readFileSync('playwright.config.js', 'utf8');
      
      if (!config.includes('localhost:8000')) {
        return 'Configuration not pointing to correct server port';
      }
      
      return true;
    });
  }

  async runQuickTest() {
    this.check('Quick test execution', async () => {
      try {
        // Run a simple smoke test
        execSync('npx playwright test specs/hub.test.js --grep "should load hub page" --timeout 30000', { 
          stdio: 'pipe',
          timeout: 30000
        });
        return true;
      } catch (error) {
        return `Test execution failed: ${error.message}`;
      }
    });
  }

  async validate() {
    console.log('🔍 Validating UniversalWallet UI Test Setup...\n');
    
    // Run all validation checks
    await this.checkNodeJS();
    await this.checkPython();
    await this.checkNpmDependencies();
    await this.checkPlaywright();
    await this.checkTestFiles();
    await this.checkMockupFiles();
    await this.checkOutputDirectory();
    await this.validateConfiguration();
    await this.checkBrowsers();
    await this.checkServer();
    
    // Optional: Run quick test if everything else passes
    if (this.results.failed === 0) {
      console.log('\n🧪 Running quick validation test...');
      await this.runQuickTest();
    }
    
    this.displayResults();
    return this.results;
  }

  displayResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 SETUP VALIDATION RESULTS');
    console.log('='.repeat(60));
    
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings}`);
    
    if (this.results.failed === 0) {
      console.log('\n🎉 Setup validation completed successfully!');
      console.log('You can now run the test suite with: npm test');
    } else {
      console.log('\n❌ Setup validation failed. Please fix the following issues:');
      this.results.issues.forEach(issue => {
        console.log(`  • ${issue.description}: ${issue.error}`);
      });
    }
    
    if (this.results.warnings > 0) {
      console.log('\n⚠️  Warnings detected. Tests may still work but consider addressing these:');
      console.log('  • Browser installation may be needed: npx playwright install');
    }
    
    console.log('\n📖 For detailed setup instructions, see README.md');
    console.log('='.repeat(60));
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new SetupValidator();
  validator.validate().catch(error => {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  });
}

module.exports = SetupValidator;

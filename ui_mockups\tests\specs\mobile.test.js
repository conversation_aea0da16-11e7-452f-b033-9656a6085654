// Mobile App Interface Tests
const { test, expect } = require('@playwright/test');
const { waitForAnimations, mobileTouch, testLoadingState } = require('../utils/test-helpers');

test.describe('Mobile App Interface', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/mobile/');
    await waitForAnimations(page);
  });

  test('should load mobile app successfully @mobile @smoke', async ({ page }) => {
    await expect(page).toHaveTitle(/UniversalWallet.*Mobile App/);
    
    // Check mobile app container
    await expect(page.locator('.mobile-app')).toBeVisible();
    
    // Check main mobile sections
    await expect(page.locator('.status-bar')).toBeVisible();
    await expect(page.locator('.app-header')).toBeVisible();
    await expect(page.locator('.app-main')).toBeVisible();
    await expect(page.locator('.bottom-nav')).toBeVisible();
  });

  test('should display status bar correctly @mobile @content', async ({ page }) => {
    const statusBar = page.locator('.status-bar');
    await expect(statusBar).toBeVisible();
    
    // Check time display
    const timeElement = statusBar.locator('.time');
    await expect(timeElement).toBeVisible();
    
    const timeText = await timeElement.textContent();
    expect(timeText).toMatch(/^\d{1,2}:\d{2}$/); // Format: HH:MM
    
    // Check status icons
    await expect(statusBar.locator('.fa-signal')).toBeVisible();
    await expect(statusBar.locator('.fa-wifi')).toBeVisible();
    await expect(statusBar.locator('.battery')).toBeVisible();
    await expect(statusBar.locator('.battery-level')).toBeVisible();
  });

  test('should display app header correctly @mobile @content', async ({ page }) => {
    const appHeader = page.locator('.app-header');
    await expect(appHeader).toBeVisible();
    
    // Check greeting
    await expect(appHeader.locator('.greeting-text')).toHaveText('Good morning');
    await expect(appHeader.locator('.user-name')).toHaveText('John Doe');
    
    // Check header actions
    const headerActions = appHeader.locator('.header-btn');
    await expect(headerActions).toHaveCount(2);
    
    // Check notification button
    const notificationBtn = headerActions.first();
    await expect(notificationBtn.locator('.fa-bell')).toBeVisible();
    await expect(notificationBtn.locator('.notification-dot')).toBeVisible();
    
    // Check profile button
    const profileBtn = headerActions.last();
    await expect(profileBtn.locator('img')).toBeVisible();
  });

  test('should display balance card correctly @mobile @content', async ({ page }) => {
    const balanceCard = page.locator('.balance-card');
    await expect(balanceCard).toBeVisible();
    
    // Check balance header
    await expect(balanceCard.locator('.balance-label')).toHaveText('Total Balance');
    await expect(balanceCard.locator('.balance-toggle')).toBeVisible();
    
    // Check balance amount
    const balanceAmount = balanceCard.locator('.balance-amount');
    await expect(balanceAmount.locator('.currency')).toHaveText('$');
    await expect(balanceAmount.locator('.amount')).toBeVisible();
    
    // Check account chips
    const accountChips = balanceCard.locator('.account-chip');
    await expect(accountChips).toHaveCount(3);
    
    const expectedChips = ['EC', 'OM', 'ZB'];
    for (let i = 0; i < expectedChips.length; i++) {
      const chip = accountChips.nth(i);
      await expect(chip.locator('.chip-icon')).toContainText(expectedChips[i]);
      await expect(chip.locator('.chip-amount')).toBeVisible();
    }
  });

  test('should have working balance toggle @mobile @interactions', async ({ page }) => {
    const balanceToggle = page.locator('.balance-toggle');
    const amounts = page.locator('.amount, .chip-amount');
    
    // Get initial state
    const firstAmount = amounts.first();
    const initialFilter = await firstAmount.evaluate(el => 
      window.getComputedStyle(el).filter
    );
    
    // Toggle balance visibility
    await balanceToggle.click();
    await page.waitForTimeout(500);
    
    // Check if filter changed (blur effect)
    const newFilter = await firstAmount.evaluate(el => 
      window.getComputedStyle(el).filter
    );
    
    expect(newFilter).not.toBe(initialFilter);
  });

  test('should display quick actions @mobile @content', async ({ page }) => {
    const quickActions = page.locator('.quick-actions');
    await expect(quickActions).toBeVisible();
    
    const actionButtons = quickActions.locator('.action-btn');
    await expect(actionButtons).toHaveCount(4);
    
    const expectedActions = ['Send', 'Receive', 'Bills', 'Scan'];
    
    for (let i = 0; i < expectedActions.length; i++) {
      const button = actionButtons.nth(i);
      await expect(button.locator('.action-icon')).toBeVisible();
      await expect(button.locator('.action-label')).toHaveText(expectedActions[i]);
    }
  });

  test('should have working quick action buttons @mobile @interactions', async ({ page }) => {
    const actionButtons = page.locator('.action-btn');
    
    // Test first action button
    const sendButton = actionButtons.first();
    await sendButton.click();
    await page.waitForTimeout(500);
    
    // Test loading state for action
    const scanButton = page.locator('.action-btn[data-action="scan"]');
    if (await scanButton.count() > 0) {
      const loadingResult = await testLoadingState(page, '.action-btn[data-action="scan"]');
      // Mobile actions might not have loading states, so we just check interaction works
      await expect(scanButton).toBeVisible();
    }
  });

  test('should display services section @mobile @content', async ({ page }) => {
    const servicesSection = page.locator('.services-section');
    await expect(servicesSection).toBeVisible();
    
    // Check section header
    await expect(servicesSection.locator('h2')).toHaveText('Services');
    await expect(servicesSection.locator('.see-all-btn')).toHaveText('See All');
    
    const serviceCards = servicesSection.locator('.service-card');
    await expect(serviceCards).toHaveCount(4);
    
    const expectedServices = [
      'Savings Groups', 'Virtual Cards', 'Analytics', 'Requests'
    ];
    
    for (let i = 0; i < expectedServices.length; i++) {
      const card = serviceCards.nth(i);
      await expect(card.locator('.service-title')).toHaveText(expectedServices[i]);
      await expect(card.locator('.service-icon')).toBeVisible();
      await expect(card.locator('.service-subtitle')).toBeVisible();
      await expect(card.locator('.service-arrow')).toBeVisible();
    }
  });

  test('should display transactions section @mobile @content', async ({ page }) => {
    const transactionsSection = page.locator('.transactions-section');
    await expect(transactionsSection).toBeVisible();
    
    // Check section header
    await expect(transactionsSection.locator('h2')).toHaveText('Recent');
    await expect(transactionsSection.locator('.see-all-btn')).toHaveText('See All');
    
    const transactionItems = transactionsSection.locator('.transaction-item');
    await expect(transactionItems).toHaveCount(4);
    
    // Check transaction structure
    for (let i = 0; i < Math.min(await transactionItems.count(), 2); i++) {
      const item = transactionItems.nth(i);
      await expect(item.locator('.transaction-icon')).toBeVisible();
      await expect(item.locator('.transaction-title')).toBeVisible();
      await expect(item.locator('.transaction-subtitle')).toBeVisible();
      await expect(item.locator('.transaction-amount')).toBeVisible();
    }
  });

  test('should have working bottom navigation @mobile @navigation', async ({ page }) => {
    const bottomNav = page.locator('.bottom-nav');
    await expect(bottomNav).toBeVisible();
    
    const navItems = bottomNav.locator('.nav-item');
    await expect(navItems).toHaveCount(5);
    
    const expectedNavItems = ['Home', 'Accounts', 'Transfer', 'History', 'More'];
    
    // Check nav items
    for (let i = 0; i < expectedNavItems.length; i++) {
      const item = navItems.nth(i);
      await expect(item.locator('i')).toBeVisible();
      await expect(item.locator('span')).toHaveText(expectedNavItems[i]);
    }
    
    // Check active state
    const homeItem = navItems.first();
    await expect(homeItem).toHaveClass(/active/);
    
    // Test navigation
    const accountsItem = navItems.nth(1);
    await accountsItem.click();
    await page.waitForTimeout(500);
    
    await expect(accountsItem).toHaveClass(/active/);
    await expect(homeItem).not.toHaveClass(/active/);
  });

  test('should have working notification overlay @mobile @interactions', async ({ page }) => {
    const notificationBtn = page.locator('#notifications-btn');
    const notificationOverlay = page.locator('#notifications-overlay');
    
    // Open notifications
    await notificationBtn.click();
    await page.waitForTimeout(500);
    
    await expect(notificationOverlay).toHaveClass(/active/);
    
    // Check overlay content
    await expect(notificationOverlay.locator('.overlay-header h3')).toHaveText('Notifications');
    
    const notificationItems = notificationOverlay.locator('.notification-item');
    await expect(notificationItems).toHaveCount(3);
    
    // Check notification structure
    for (let i = 0; i < Math.min(await notificationItems.count(), 2); i++) {
      const item = notificationItems.nth(i);
      await expect(item.locator('.notification-icon')).toBeVisible();
      await expect(item.locator('.notification-title')).toBeVisible();
      await expect(item.locator('.notification-text')).toBeVisible();
      await expect(item.locator('.notification-time')).toBeVisible();
    }
    
    // Close notifications
    const closeButton = notificationOverlay.locator('.close-overlay');
    await closeButton.click();
    await page.waitForTimeout(500);
    
    await expect(notificationOverlay).not.toHaveClass(/active/);
  });

  test('should have touch interactions @mobile @interactions', async ({ page }) => {
    // Test service card touch
    const serviceCards = page.locator('.service-card');
    if (await serviceCards.count() > 0) {
      const firstCard = serviceCards.first();
      
      // Simulate touch interaction
      await firstCard.click();
      await page.waitForTimeout(300);
    }
    
    // Test transaction item touch
    const transactionItems = page.locator('.transaction-item');
    if (await transactionItems.count() > 0) {
      const firstTransaction = transactionItems.first();
      
      await firstTransaction.click();
      await page.waitForTimeout(300);
    }
  });

  test('should be properly sized for mobile @mobile @responsive', async ({ page }) => {
    // Check mobile app container dimensions
    const mobileApp = page.locator('.mobile-app');
    const appBox = await mobileApp.boundingBox();
    
    // Should fit within mobile viewport
    expect(appBox.width).toBeLessThanOrEqual(375);
    expect(appBox.height).toBeLessThanOrEqual(812);
    
    // Check if content fits within container
    const appMain = page.locator('.app-main');
    const mainBox = await appMain.boundingBox();
    
    expect(mainBox.width).toBeLessThanOrEqual(appBox.width);
  });

  test('should have mobile-specific styling @mobile @visual', async ({ page }) => {
    // Check mobile app has rounded corners
    const mobileApp = page.locator('.mobile-app');
    const borderRadius = await mobileApp.evaluate(el => 
      window.getComputedStyle(el).borderRadius
    );
    
    expect(borderRadius).not.toBe('0px');
    
    // Check balance card has gradient background
    const balanceCard = page.locator('.balance-card');
    const background = await balanceCard.evaluate(el => 
      window.getComputedStyle(el).background
    );
    
    expect(background).toContain('gradient');
  });

  test('should handle time updates @mobile @functionality', async ({ page }) => {
    const timeElement = page.locator('.time');
    const initialTime = await timeElement.textContent();
    
    // Wait for potential time update
    await page.waitForTimeout(2000);
    
    // Time should still be in correct format
    const currentTime = await timeElement.textContent();
    expect(currentTime).toMatch(/^\d{1,2}:\d{2}$/);
  });

  test('should have working see all buttons @mobile @interactions', async ({ page }) => {
    const seeAllButtons = page.locator('.see-all-btn');
    
    for (let i = 0; i < Math.min(await seeAllButtons.count(), 2); i++) {
      const button = seeAllButtons.nth(i);
      await button.click();
      await page.waitForTimeout(300);
    }
  });

  test('should load without JavaScript errors @mobile @smoke', async ({ page }) => {
    const errors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.reload();
    await waitForAnimations(page);
    
    expect(errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_')
    )).toHaveLength(0);
  });

  test('should have proper mobile viewport @mobile @responsive', async ({ page }) => {
    // Test different mobile viewport sizes
    const viewports = [
      { width: 375, height: 667 }, // iPhone SE
      { width: 390, height: 844 }, // iPhone 12
      { width: 414, height: 896 }  // iPhone 11 Pro Max
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(300);
      
      // Check mobile app is still visible and properly sized
      const mobileApp = page.locator('.mobile-app');
      await expect(mobileApp).toBeVisible();
      
      const appBox = await mobileApp.boundingBox();
      expect(appBox.width).toBeLessThanOrEqual(viewport.width);
      expect(appBox.height).toBeLessThanOrEqual(viewport.height);
    }
  });

  test('should have touch feedback @mobile @interactions', async ({ page }) => {
    // Check if touch feedback element exists
    const touchFeedback = page.locator('#touch-feedback');
    await expect(touchFeedback).toBeVisible();
    
    // Test touch on action button
    const actionButton = page.locator('.action-btn').first();
    await actionButton.click();
    await page.waitForTimeout(100);
    
    // Touch feedback should be positioned (though animation might be quick)
    const feedbackBox = await touchFeedback.boundingBox();
    expect(feedbackBox).toBeTruthy();
  });
});

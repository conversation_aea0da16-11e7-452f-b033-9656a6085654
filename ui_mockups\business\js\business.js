// Business Dashboard JavaScript

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeBusinessDashboard();
    setupAnimations();
    setupInteractions();
    setupNavigation();
});

// Initialize Business Dashboard
function initializeBusinessDashboard() {
    // Set initial active page
    showPage('dashboard');
    
    // Initialize sidebar state
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
    
    // Load saved sidebar state
    const sidebarCollapsed = localStorage.getItem('businessSidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
    }
    
    // Setup sidebar toggle
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        localStorage.setItem('businessSidebarCollapsed', sidebar.classList.contains('collapsed'));
    });
    
    // Setup mobile sidebar toggle
    mobileSidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('mobile-open');
    });
    
    // Close mobile sidebar when clicking outside
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768 && 
            !sidebar.contains(e.target) && 
            !mobileSidebarToggle.contains(e.target)) {
            sidebar.classList.remove('mobile-open');
        }
    });
    
    // Initialize business-specific features
    setupMetricToggle();
    setupApprovalHandlers();
    startBusinessUpdates();
}

// Setup GSAP Animations
function setupAnimations() {
    // Animate sidebar menu items on load
    gsap.from('.menu-item', {
        duration: 0.6,
        x: -30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 0.3
    });
    
    // Animate business badge
    gsap.from('.business-badge', {
        duration: 0.5,
        scale: 0,
        opacity: 0,
        ease: 'back.out(1.7)',
        delay: 0.5
    });
    
    // Animate metric cards
    gsap.from('.metric-card', {
        duration: 0.8,
        y: 40,
        opacity: 0,
        stagger: 0.15,
        ease: 'power3.out',
        delay: 0.6
    });
    
    // Animate quick actions
    gsap.from('.action-card', {
        duration: 0.6,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 0.9
    });
    
    // Animate account cards
    gsap.from('.account-card', {
        duration: 0.8,
        y: 40,
        opacity: 0,
        stagger: 0.15,
        ease: 'power3.out',
        delay: 1.1
    });
    
    // Animate activity items
    gsap.from('.activity-item', {
        duration: 0.6,
        x: -20,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 1.3
    });
    
    // Animate approval items
    gsap.from('.approval-item', {
        duration: 0.6,
        x: -20,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 1.5
    });
    
    // Animate numbers counting up
    animateBusinessCounters();
}

// Setup Interactions
function setupInteractions() {
    // Action card hover effects
    const actionCards = document.querySelectorAll('.action-card');
    
    actionCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1.02,
                ease: 'power2.out'
            });
            
            gsap.to(this.querySelector('.action-icon'), {
                duration: 0.3,
                scale: 1.1,
                ease: 'power2.out'
            });
        });

        card.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1,
                ease: 'power2.out'
            });
            
            gsap.to(this.querySelector('.action-icon'), {
                duration: 0.3,
                scale: 1,
                ease: 'power2.out'
            });
        });
        
        // Add click handler for business actions
        card.addEventListener('click', function() {
            const action = this.dataset.action;
            handleBusinessAction(action);
        });
    });
    
    // Account card hover effects
    const accountCards = document.querySelectorAll('.account-card');
    
    accountCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('primary-account')) {
                gsap.to(this, {
                    duration: 0.4,
                    y: -8,
                    ease: 'power2.out'
                });
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('primary-account')) {
                gsap.to(this, {
                    duration: 0.4,
                    y: 0,
                    ease: 'power2.out'
                });
            }
        });
    });
    
    // Metric card hover effects
    const metricCards = document.querySelectorAll('.metric-card');
    
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('primary')) {
                gsap.to(this, {
                    duration: 0.3,
                    y: -6,
                    ease: 'power2.out'
                });
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('primary')) {
                gsap.to(this, {
                    duration: 0.3,
                    y: 0,
                    ease: 'power2.out'
                });
            }
        });
    });
    
    // Activity and approval item hover effects
    const listItems = document.querySelectorAll('.activity-item, .approval-item');
    
    listItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.2,
                x: 4,
                ease: 'power2.out'
            });
        });

        item.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.2,
                x: 0,
                ease: 'power2.out'
            });
        });
    });
    
    // Button click effects with ripple
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('div');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(37, 99, 235, 0.3);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
                width: ${size}px;
                height: ${size}px;
                left: ${e.clientX - rect.left - size / 2}px;
                top: ${e.clientY - rect.top - size / 2}px;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add ripple animation CSS if not exists
    if (!document.querySelector('#business-ripple-styles')) {
        const styles = document.createElement('style');
        styles.id = 'business-ripple-styles';
        styles.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(styles);
    }
}

// Setup Navigation
function setupNavigation() {
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all items
            menuItems.forEach(mi => mi.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Get page name
            const page = this.dataset.page;
            
            // Show corresponding page
            showPage(page);
            
            // Close mobile sidebar
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        });
    });
}

// Show Page Function
function showPage(pageName) {
    // Hide all pages
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });
    
    // Show selected page
    const targetPage = document.getElementById(`${pageName}-page`);
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // Update page title
    updateBusinessPageTitle(pageName);
    
    // Animate page transition
    gsap.from('.page.active', {
        duration: 0.5,
        opacity: 0,
        y: 20,
        ease: 'power3.out'
    });
}

// Update Business Page Title
function updateBusinessPageTitle(pageName) {
    const pageTitle = document.getElementById('page-title');
    const pageSubtitle = document.getElementById('page-subtitle');
    
    const titles = {
        dashboard: {
            title: 'Business Dashboard',
            subtitle: 'Comprehensive overview of your business finances and operations.'
        },
        accounts: {
            title: 'Business Accounts',
            subtitle: 'Manage your business accounts and financial connections.'
        },
        analytics: {
            title: 'Business Analytics',
            subtitle: 'Deep insights into your business performance and trends.'
        },
        'bulk-payments': {
            title: 'Bulk Payments',
            subtitle: 'Process multiple payments efficiently to suppliers and employees.'
        },
        invoicing: {
            title: 'Invoicing',
            subtitle: 'Create, send, and manage business invoices.'
        },
        payroll: {
            title: 'Payroll Management',
            subtitle: 'Process employee payments and manage payroll operations.'
        },
        suppliers: {
            title: 'Supplier Payments',
            subtitle: 'Manage payments to your business suppliers and vendors.'
        },
        team: {
            title: 'Team Management',
            subtitle: 'Manage team members, roles, and permissions.'
        },
        approvals: {
            title: 'Approval Workflows',
            subtitle: 'Review and approve pending business transactions.'
        },
        reports: {
            title: 'Business Reports',
            subtitle: 'Generate comprehensive business and financial reports.'
        },
        transactions: {
            title: 'Transaction History',
            subtitle: 'View complete business transaction history and details.'
        },
        settings: {
            title: 'Business Settings',
            subtitle: 'Configure business account preferences and security.'
        }
    };
    
    const pageInfo = titles[pageName] || titles.dashboard;
    pageTitle.textContent = pageInfo.title;
    pageSubtitle.textContent = pageInfo.subtitle;
}

// Handle Business Actions
function handleBusinessAction(action) {
    console.log(`Business action: ${action}`);
    
    // Add loading state
    const actionCard = document.querySelector(`[data-action="${action}"]`);
    const originalContent = actionCard.innerHTML;
    
    actionCard.innerHTML = `
        <div class="action-icon">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <div class="action-content">
            <div class="action-title">Processing...</div>
            <div class="action-subtitle">Please wait</div>
        </div>
    `;
    
    // Simulate action processing
    setTimeout(() => {
        actionCard.innerHTML = originalContent;
        
        // Show success feedback
        showBusinessNotification(`${action.replace('-', ' ')} initiated successfully!`, 'success');
    }, 2000);
}

// Setup Metric Toggle
function setupMetricToggle() {
    const metricToggle = document.querySelector('.metric-toggle');
    let metricsVisible = true;
    
    if (metricToggle) {
        metricToggle.addEventListener('click', function() {
            metricsVisible = !metricsVisible;
            
            const amounts = document.querySelectorAll('.amount, .balance-value');
            const icon = this.querySelector('i');
            
            amounts.forEach(amount => {
                if (metricsVisible) {
                    amount.style.filter = 'none';
                    icon.className = 'fas fa-eye';
                } else {
                    amount.style.filter = 'blur(8px)';
                    icon.className = 'fas fa-eye-slash';
                }
            });
        });
    }
}

// Setup Approval Handlers
function setupApprovalHandlers() {
    const approvalButtons = document.querySelectorAll('.approval-actions .btn');
    
    approvalButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            
            const action = this.textContent.toLowerCase();
            const approvalItem = this.closest('.approval-item');
            const approvalTitle = approvalItem.querySelector('.approval-title').textContent;
            
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;
            
            // Simulate approval processing
            setTimeout(() => {
                // Remove the approval item with animation
                gsap.to(approvalItem, {
                    duration: 0.5,
                    x: action === 'approve' ? 100 : -100,
                    opacity: 0,
                    ease: 'power3.in',
                    onComplete: () => {
                        approvalItem.remove();
                        updateApprovalCount();
                    }
                });
                
                // Show notification
                const message = `${approvalTitle} has been ${action}d`;
                showBusinessNotification(message, action === 'approve' ? 'success' : 'warning');
            }, 1500);
        });
    });
}

// Update Approval Count
function updateApprovalCount() {
    const approvalCount = document.querySelector('.approval-count');
    const notificationCount = document.querySelector('.notification-count');
    const remainingApprovals = document.querySelectorAll('.approval-item').length;
    
    if (approvalCount) {
        approvalCount.textContent = `${remainingApprovals} pending`;
    }
    
    if (notificationCount) {
        notificationCount.textContent = remainingApprovals.toString();
        if (remainingApprovals === 0) {
            notificationCount.style.display = 'none';
        }
    }
}

// Animate Business Counters
function animateBusinessCounters() {
    const counters = document.querySelectorAll('.amount, .balance-value');
    
    counters.forEach(counter => {
        const target = parseFloat(counter.textContent.replace(/[^\d.]/g, ''));
        const prefix = counter.textContent.match(/[^\d.]/g)?.join('') || '';
        
        gsap.to({ value: 0 }, {
            duration: 2.5,
            value: target,
            ease: 'power2.out',
            onUpdate: function() {
                const currentValue = this.targets()[0].value;
                if (target >= 1000) {
                    counter.textContent = prefix + currentValue.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                } else {
                    counter.textContent = prefix + currentValue.toFixed(0);
                }
            },
            delay: 0.7
        });
    });
}

// Start Business Updates
function startBusinessUpdates() {
    // Simulate real-time business metrics updates
    setInterval(() => {
        updateBusinessMetrics();
    }, 45000); // Update every 45 seconds
    
    // Simulate new business activity
    setInterval(() => {
        addNewBusinessActivity();
    }, 90000); // Add new activity every 1.5 minutes
}

// Update Business Metrics
function updateBusinessMetrics() {
    const metricElements = document.querySelectorAll('.metric-value .amount, .balance-value');
    
    metricElements.forEach(element => {
        const currentValue = parseFloat(element.textContent.replace(/[^\d.]/g, ''));
        const change = (Math.random() - 0.4) * (currentValue * 0.02); // Smaller changes for business
        const newValue = Math.max(0, currentValue + change);
        const prefix = element.textContent.match(/[^\d.]/g)?.join('') || '';
        
        gsap.to({ value: currentValue }, {
            duration: 1.5,
            value: newValue,
            ease: 'power2.out',
            onUpdate: function() {
                const current = this.targets()[0].value;
                if (current >= 1000) {
                    element.textContent = prefix + current.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                } else {
                    element.textContent = prefix + current.toFixed(0);
                }
            }
        });
    });
}

// Add New Business Activity
function addNewBusinessActivity() {
    const activityList = document.querySelector('.activity-list');
    if (!activityList) return;
    
    const newActivity = document.createElement('div');
    newActivity.className = 'activity-item';
    newActivity.style.opacity = '0';
    newActivity.style.transform = 'translateX(-20px)';
    
    const activities = [
        {
            icon: 'invoice',
            iconClass: 'fas fa-file-invoice',
            title: 'New invoice payment received',
            subtitle: 'Just now • Invoice #INV-' + Math.floor(Math.random() * 1000),
            amount: '+$' + (Math.random() * 5000 + 1000).toFixed(2),
            amountClass: 'positive'
        },
        {
            icon: 'supplier',
            iconClass: 'fas fa-truck',
            title: 'Supplier payment processed',
            subtitle: 'Just now • Supplier payment',
            amount: '-$' + (Math.random() * 2000 + 500).toFixed(2),
            amountClass: 'negative'
        }
    ];
    
    const activity = activities[Math.floor(Math.random() * activities.length)];
    
    newActivity.innerHTML = `
        <div class="activity-icon ${activity.icon}">
            <i class="${activity.iconClass}"></i>
        </div>
        <div class="activity-details">
            <div class="activity-title">${activity.title}</div>
            <div class="activity-subtitle">${activity.subtitle}</div>
        </div>
        <div class="activity-amount ${activity.amountClass}">${activity.amount}</div>
        <div class="activity-status completed">
            <i class="fas fa-check"></i>
        </div>
    `;
    
    activityList.insertBefore(newActivity, activityList.firstChild);
    
    // Animate in
    gsap.to(newActivity, {
        duration: 0.5,
        opacity: 1,
        x: 0,
        ease: 'power3.out'
    });
    
    // Remove oldest activity if more than 4
    const activityItems = activityList.querySelectorAll('.activity-item');
    if (activityItems.length > 4) {
        const lastActivity = activityItems[activityItems.length - 1];
        gsap.to(lastActivity, {
            duration: 0.3,
            opacity: 0,
            x: 20,
            ease: 'power3.in',
            onComplete: () => lastActivity.remove()
        });
    }
}

// Show Business Notification
function showBusinessNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `business-notification business-notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add business notification styles if not exists
    if (!document.querySelector('#business-notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'business-notification-styles';
        styles.textContent = `
            .business-notification {
                position: fixed;
                top: 100px;
                right: 20px;
                background: var(--glass-bg-strong);
                backdrop-filter: var(--glass-backdrop);
                border: 1px solid var(--glass-border);
                border-radius: var(--radius-lg);
                padding: var(--space-4);
                display: flex;
                align-items: center;
                gap: var(--space-3);
                z-index: var(--z-toast);
                min-width: 350px;
                box-shadow: var(--shadow-lg);
                transform: translateX(100%);
                transition: transform var(--transition-base);
            }
            
            .business-notification.show {
                transform: translateX(0);
            }
            
            .business-notification-success {
                border-left: 4px solid var(--color-success);
            }
            
            .business-notification-warning {
                border-left: 4px solid var(--color-warning);
            }
            
            .business-notification .notification-content {
                display: flex;
                align-items: center;
                gap: var(--space-2);
                flex: 1;
                color: var(--color-text-primary);
                font-size: var(--font-size-sm);
            }
            
            .business-notification-success .notification-content i {
                color: var(--color-success);
            }
            
            .business-notification-warning .notification-content i {
                color: var(--color-warning);
            }
            
            .business-notification .notification-close {
                background: none;
                border: none;
                color: var(--color-text-secondary);
                cursor: pointer;
                padding: var(--space-1);
                border-radius: var(--radius-base);
                transition: all var(--transition-base);
            }
            
            .business-notification .notification-close:hover {
                background: var(--glass-bg);
                color: var(--color-text-primary);
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Auto hide after 4 seconds
    setTimeout(() => {
        hideBusinessNotification(notification);
    }, 4000);
    
    // Close button handler
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideBusinessNotification(notification);
    });
}

// Hide Business Notification
function hideBusinessNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
}

// Resize handler
window.addEventListener('resize', function() {
    ScrollTrigger.refresh();
    
    // Close mobile sidebar on resize to desktop
    if (window.innerWidth > 768) {
        document.getElementById('sidebar').classList.remove('mobile-open');
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('Business Dashboard Error:', e.error);
    showBusinessNotification('An error occurred. Please refresh the page.', 'warning');
});

// Cross-Browser and Device Testing
const { test, expect } = require('@playwright/test');
const { waitForAnimations, hasGlassmorphism, testResponsive, checkAccessibility } = require('../utils/test-helpers');

test.describe('Cross-Browser Compatibility', () => {
  
  test.describe('Browser Feature Support', () => {
    test('should support CSS Grid @desktop @cross-browser', async ({ page, browserName }) => {
      await page.goto('/');
      
      const supportsGrid = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.display = 'grid';
        return testEl.style.display === 'grid';
      });
      
      expect(supportsGrid).toBe(true);
      
      // Test grid layouts work
      const mockupsGrid = page.locator('.mockups-grid');
      await expect(mockupsGrid).toBeVisible();
      
      const gridDisplay = await mockupsGrid.evaluate(el => 
        window.getComputedStyle(el).display
      );
      expect(gridDisplay).toBe('grid');
    });

    test('should support CSS Flexbox @desktop @cross-browser', async ({ page, browserName }) => {
      await page.goto('/individual/');
      await waitForAnimations(page);
      
      const supportsFlex = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.display = 'flex';
        return testEl.style.display === 'flex';
      });
      
      expect(supportsFlex).toBe(true);
      
      // Test flex layouts work
      const headerContent = page.locator('.header-content');
      const flexDisplay = await headerContent.evaluate(el => 
        window.getComputedStyle(el).display
      );
      expect(flexDisplay).toBe('flex');
    });

    test('should support CSS Custom Properties @desktop @cross-browser', async ({ page, browserName }) => {
      await page.goto('/marketing/');
      
      const supportsCustomProps = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.setProperty('--test-prop', 'test');
        return testEl.style.getPropertyValue('--test-prop') === 'test';
      });
      
      expect(supportsCustomProps).toBe(true);
      
      // Test custom properties are applied
      const primaryColor = await page.evaluate(() => 
        getComputedStyle(document.documentElement).getPropertyValue('--color-primary')
      );
      
      expect(primaryColor.trim()).not.toBe('');
    });

    test('should support backdrop-filter @desktop @cross-browser', async ({ page, browserName }) => {
      await page.goto('/individual/');
      await waitForAnimations(page);
      
      const supportsBackdropFilter = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.backdropFilter = 'blur(10px)';
        return testEl.style.backdropFilter !== '';
      });
      
      // Webkit browsers should support backdrop-filter
      if (browserName === 'webkit' || browserName === 'chromium') {
        expect(supportsBackdropFilter).toBe(true);
        
        // Test glassmorphism effects
        const sidebar = page.locator('.sidebar');
        expect(await hasGlassmorphism(sidebar)).toBe(true);
      }
    });

    test('should handle JavaScript ES6+ features @desktop @cross-browser', async ({ page, browserName }) => {
      await page.goto('/marketing/');
      
      const supportsES6 = await page.evaluate(() => {
        try {
          // Test arrow functions
          const arrow = () => true;
          
          // Test const/let
          const testConst = 'test';
          let testLet = 'test';
          
          // Test template literals
          const template = `test ${testConst}`;
          
          // Test destructuring
          const { length } = 'test';
          
          return arrow() && testConst && testLet && template && length;
        } catch (e) {
          return false;
        }
      });
      
      expect(supportsES6).toBe(true);
    });
  });

  test.describe('Responsive Design Testing', () => {
    const viewports = [
      { name: 'Mobile Small', width: 320, height: 568 },
      { name: 'Mobile Medium', width: 375, height: 667 },
      { name: 'Mobile Large', width: 414, height: 896 },
      { name: 'Tablet Portrait', width: 768, height: 1024 },
      { name: 'Tablet Landscape', width: 1024, height: 768 },
      { name: 'Desktop Small', width: 1366, height: 768 },
      { name: 'Desktop Large', width: 1920, height: 1080 }
    ];

    test('should be responsive across all viewports @responsive', async ({ page }) => {
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto('/');
        await page.waitForTimeout(500);
        
        // Check main elements are visible
        await expect(page.locator('.hub-header')).toBeVisible();
        await expect(page.locator('.mockups-grid')).toBeVisible();
        
        // Check layout adapts to viewport
        const mockupsGrid = page.locator('.mockups-grid');
        const gridColumns = await mockupsGrid.evaluate(el => 
          window.getComputedStyle(el).gridTemplateColumns
        );
        
        // Grid should adapt to viewport size
        expect(gridColumns).not.toBe('none');
      }
    });

    test('should handle marketing page responsively @responsive', async ({ page }) => {
      const testViewports = viewports.slice(0, 4); // Test subset for performance
      
      for (const viewport of testViewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto('/marketing/');
        await waitForAnimations(page);
        
        // Check hero section adapts
        await expect(page.locator('.hero')).toBeVisible();
        await expect(page.locator('.hero-title')).toBeVisible();
        
        // Check navigation adapts
        if (viewport.width <= 768) {
          // Mobile: should show mobile menu toggle
          const mobileToggle = page.locator('.mobile-menu-toggle');
          await expect(mobileToggle).toBeVisible();
        } else {
          // Desktop: should show full navigation
          const navMenu = page.locator('.nav-menu');
          await expect(navMenu).toBeVisible();
        }
      }
    });

    test('should handle dashboard responsively @responsive', async ({ page }) => {
      const testViewports = [
        { width: 375, height: 667 }, // Mobile
        { width: 768, height: 1024 }, // Tablet
        { width: 1366, height: 768 }  // Desktop
      ];
      
      for (const viewport of testViewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.goto('/individual/');
        await waitForAnimations(page);
        
        if (viewport.width <= 768) {
          // Mobile: sidebar should be hidden, mobile toggle visible
          const mobileSidebarToggle = page.locator('.mobile-sidebar-toggle');
          await expect(mobileSidebarToggle).toBeVisible();
        } else {
          // Desktop: sidebar should be visible
          const sidebar = page.locator('.sidebar');
          await expect(sidebar).toBeVisible();
        }
        
        // Main content should always be visible
        await expect(page.locator('.main-content')).toBeVisible();
        await expect(page.locator('.balance-overview')).toBeVisible();
      }
    });
  });

  test.describe('Touch and Mobile Interactions', () => {
    test('should handle touch events on mobile @mobile @touch', async ({ page }) => {
      await page.goto('/mobile/');
      await waitForAnimations(page);
      
      // Test touch on action buttons
      const actionButtons = page.locator('.action-btn');
      const firstButton = actionButtons.first();
      
      // Simulate touch
      await firstButton.click();
      await page.waitForTimeout(300);
      
      // Should remain visible and functional
      await expect(firstButton).toBeVisible();
    });

    test('should support swipe gestures @mobile @touch', async ({ page }) => {
      await page.goto('/mobile/');
      await waitForAnimations(page);
      
      // Test notification overlay swipe
      const notificationBtn = page.locator('#notifications-btn');
      await notificationBtn.click();
      
      const overlay = page.locator('#notifications-overlay');
      await expect(overlay).toHaveClass(/active/);
      
      // Simulate swipe down (simplified)
      const overlayBox = await overlay.boundingBox();
      if (overlayBox) {
        await page.mouse.move(overlayBox.x + overlayBox.width / 2, overlayBox.y + 100);
        await page.mouse.down();
        await page.mouse.move(overlayBox.x + overlayBox.width / 2, overlayBox.y + 300);
        await page.mouse.up();
        
        await page.waitForTimeout(500);
        
        // Overlay might close on swipe
        // (Implementation depends on actual swipe handling)
      }
    });

    test('should prevent zoom on mobile @mobile @touch', async ({ page }) => {
      await page.goto('/mobile/');
      
      // Check viewport meta tag prevents zoom
      const viewportMeta = await page.locator('meta[name="viewport"]').getAttribute('content');
      expect(viewportMeta).toContain('user-scalable=no');
    });
  });

  test.describe('Accessibility Testing', () => {
    test('should be accessible across browsers @desktop @accessibility', async ({ page, browserName }) => {
      await page.goto('/');
      
      const accessibility = await checkAccessibility(page);
      
      // Should have ARIA elements
      expect(accessibility.ariaElements).toBeGreaterThan(0);
      
      // Should have focusable elements
      expect(accessibility.focusableElements).toBeGreaterThan(0);
      
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      const focusedElement = await page.locator(':focus').count();
      expect(focusedElement).toBe(1);
    });

    test('should support screen readers @desktop @accessibility', async ({ page }) => {
      await page.goto('/individual/');
      await waitForAnimations(page);
      
      // Check for proper heading structure
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0);
      
      // Check for alt text on images
      const images = page.locator('img');
      for (let i = 0; i < Math.min(await images.count(), 3); i++) {
        const img = images.nth(i);
        const alt = await img.getAttribute('alt');
        expect(alt).not.toBeNull();
      }
    });

    test('should have proper color contrast @desktop @accessibility', async ({ page }) => {
      await page.goto('/marketing/');
      
      // Test primary text contrast
      const heroTitle = page.locator('.hero-title');
      const titleColor = await heroTitle.evaluate(el => {
        const style = window.getComputedStyle(el);
        return {
          color: style.color,
          backgroundColor: style.backgroundColor
        };
      });
      
      // Should have defined colors
      expect(titleColor.color).not.toBe('');
    });
  });

  test.describe('Performance Across Browsers', () => {
    test('should load quickly in all browsers @desktop @performance', async ({ page, browserName }) => {
      const startTime = Date.now();
      
      await page.goto('/marketing/');
      await waitForAnimations(page);
      
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time across browsers
      expect(loadTime).toBeLessThan(8000);
      
      // Check if main content is visible
      await expect(page.locator('.hero')).toBeVisible();
    });

    test('should handle animations smoothly @desktop @performance', async ({ page, browserName }) => {
      await page.goto('/individual/');
      await waitForAnimations(page);
      
      // Test hover animation performance
      const actionCard = page.locator('.action-card').first();
      
      const startTime = Date.now();
      await actionCard.hover();
      await page.waitForTimeout(500);
      const animationTime = Date.now() - startTime;
      
      // Animation should complete quickly
      expect(animationTime).toBeLessThan(1000);
    });
  });

  test.describe('Error Handling', () => {
    test('should handle JavaScript errors gracefully @desktop @error-handling', async ({ page, browserName }) => {
      const errors = [];
      
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      page.on('pageerror', error => {
        errors.push(error.message);
      });
      
      await page.goto('/marketing/');
      await waitForAnimations(page);
      
      // Should have minimal errors
      const significantErrors = errors.filter(error => 
        !error.includes('favicon') && 
        !error.includes('404') &&
        !error.includes('net::ERR_') &&
        !error.includes('chrome-extension')
      );
      
      expect(significantErrors).toHaveLength(0);
    });

    test('should handle network failures gracefully @desktop @error-handling', async ({ page }) => {
      // Test with offline mode
      await page.context().setOffline(true);
      
      try {
        await page.goto('/marketing/', { timeout: 5000 });
      } catch (error) {
        // Expected to fail offline
        expect(error.message).toContain('net::ERR_INTERNET_DISCONNECTED');
      }
      
      // Restore online
      await page.context().setOffline(false);
      
      // Should work when back online
      await page.goto('/marketing/');
      await expect(page.locator('.hero')).toBeVisible();
    });
  });
});

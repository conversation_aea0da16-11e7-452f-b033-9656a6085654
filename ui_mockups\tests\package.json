{"name": "universalwallet-ui-tests", "version": "1.0.0", "description": "Comprehensive Playwright test suite for UniversalWallet UI mockups", "main": "index.js", "scripts": {"test": "node run-tests.js", "test:playwright": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "test:mobile": "node run-tests.js mobile", "test:desktop": "node run-tests.js desktop", "test:animations": "node run-tests.js animations", "test:forms": "node run-tests.js forms", "test:navigation": "node run-tests.js navigation", "test:responsive": "node run-tests.js responsive", "test:accessibility": "node run-tests.js accessibility", "test:performance": "node run-tests.js performance", "test:smoke": "node run-tests.js smoke", "test:cross-browser": "playwright test --grep @cross-browser", "install": "playwright install", "setup": "npm install && playwright install", "start-server": "cd .. && python -m http.server 8000", "check-server": "curl -f http://localhost:8000 || echo 'Server not running'", "clean": "rm -rf test-results && mkdir test-results"}, "keywords": ["playwright", "testing", "ui", "fintech", "universalwallet"], "author": "UniversalWallet Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}, "dependencies": {}}
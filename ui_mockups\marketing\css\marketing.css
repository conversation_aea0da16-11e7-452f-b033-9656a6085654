/* Marketing Website Styles */

/* Preloader */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-neutral-900), var(--color-neutral-800));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: opacity 0.8s ease-in-out;
}

.preloader.loaded {
  opacity: 0;
  pointer-events: none;
}

.loader-container {
  text-align: center;
}

.loader {
  width: 60px;
  height: 60px;
  position: relative;
  margin: 0 auto var(--space-6);
  animation: rotation 1.5s linear infinite;
}

.loader::after,
.loader::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  top: 50%;
  left: 50%;
  transform: scale(0.5) translate(0, 0);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 50%;
  animation: animloader 1.5s infinite ease-in-out;
}

.loader::before {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
  transform: scale(0.5) translate(-60px, -60px);
}

.loader-text {
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

@keyframes rotation {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes animloader {
  50% { transform: scale(1) translate(-50%, -50%); }
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  z-index: var(--z-fixed);
  transition: all var(--transition-base);
}

.navbar.scrolled {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  box-shadow: var(--shadow-lg);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.nav-brand .back-to-hub {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--glass-bg);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: all var(--transition-base);
  font-size: var(--font-size-lg);
}

.nav-brand .back-to-hub:hover {
  background: var(--color-primary);
  color: white;
  transform: scale(1.05);
}

.nav-brand .logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-decoration: none;
}

.nav-brand .logo i {
  color: var(--color-primary);
  font-size: var(--font-size-2xl);
}

.nav-menu {
  display: flex;
  gap: var(--space-8);
}

.nav-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-base);
  position: relative;
}

.nav-link:hover {
  color: var(--color-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width var(--transition-base);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
}

.mobile-menu-toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text-primary);
  transition: all var(--transition-base);
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    var(--color-neutral-50) 0%, 
    var(--color-neutral-100) 50%, 
    var(--color-primary-alpha) 100%);
}

.hero-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary-alpha), var(--color-secondary-alpha));
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 5;
}

.hero-content {
  max-width: 600px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  margin-bottom: var(--space-6);
}

.hero-badge i {
  color: var(--color-warning);
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--space-6);
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-8);
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-base);
}

.hero-stats {
  display: flex;
  gap: var(--space-8);
}

.stat {
  text-align: center;
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Hero Visual */
.hero-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-mockup {
  position: relative;
  z-index: 10;
}

.phone-frame {
  width: 280px;
  height: 560px;
  background: linear-gradient(135deg, var(--color-neutral-800), var(--color-neutral-900));
  border-radius: 40px;
  padding: 20px;
  box-shadow: var(--shadow-2xl);
  position: relative;
}

.phone-frame::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--color-neutral-600);
  border-radius: 2px;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-neutral-50), var(--color-neutral-100));
  border-radius: 30px;
  overflow: hidden;
  position: relative;
}

.app-interface {
  padding: var(--space-6) var(--space-4);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.app-header .balance-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  text-align: center;
}

.balance-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.balance-amount {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.app-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-3);
}

.action-btn {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-base);
}

.action-btn:hover {
  background: var(--glass-bg-strong);
  transform: translateY(-2px);
}

.action-btn i {
  font-size: var(--font-size-lg);
  color: var(--color-primary);
  margin-bottom: var(--space-2);
  display: block;
}

.action-btn span {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.app-accounts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.account-item {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.account-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  color: white;
  font-size: var(--font-size-sm);
}

.account-icon.ecocash {
  background: linear-gradient(135deg, #FF6B35, #F7931E);
}

.account-icon.onemoney {
  background: linear-gradient(135deg, #1E3A8A, #3B82F6);
}

.account-icon.bank {
  background: linear-gradient(135deg, #059669, #10B981);
}

.account-icon.ecocash::after { content: 'EC'; }
.account-icon.onemoney::after { content: 'OM'; }
.account-icon.bank::after { content: 'ZB'; }

.account-info {
  flex: 1;
}

.account-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.account-balance {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

/* Floating Cards */
.floating-cards {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-card {
  position: absolute;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  box-shadow: var(--shadow-lg);
  animation: floatCard 4s ease-in-out infinite;
}

.floating-card i {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.card-1 {
  top: 10%;
  right: -20%;
  animation-delay: 0s;
}

.card-2 {
  top: 50%;
  left: -25%;
  animation-delay: 1.5s;
}

.card-3 {
  bottom: 15%;
  right: -15%;
  animation-delay: 3s;
}

@keyframes floatCard {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  50% { transform: translateY(-15px) translateX(10px); }
}

/* Sections */
.section {
  padding: var(--space-24) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.section-badge {
  display: inline-block;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  margin-bottom: var(--space-6);
}

.section-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-6);
  line-height: var(--line-height-tight);
}

.section-description {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
}

/* Features Section */
.features {
  padding: var(--space-24) 0;
  background: linear-gradient(135deg, var(--color-neutral-50), var(--color-neutral-100));
  position: relative;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 80% 20%, var(--color-secondary-alpha) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, var(--color-primary-alpha) 0%, transparent 50%);
  pointer-events: none;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  position: relative;
  z-index: 5;
}

.feature-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
}

.feature-card:hover {
  transform: translateY(-8px);
  background: var(--glass-bg-strong);
  box-shadow: var(--shadow-2xl);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
}

.feature-icon i {
  font-size: var(--font-size-2xl);
  color: var(--color-neutral-900);
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.feature-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
}

.feature-benefits {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.benefit {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.benefit i {
  color: var(--color-success);
  font-size: var(--font-size-sm);
}

/* How It Works Section */
.how-it-works {
  padding: var(--space-24) 0;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-12);
  margin-top: var(--space-16);
}

.step {
  text-align: center;
  position: relative;
}

.step::after {
  content: '';
  position: absolute;
  top: 30px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), transparent);
  z-index: 1;
}

.step:last-child::after {
  display: none;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-900);
  margin: 0 auto var(--space-6);
  position: relative;
  z-index: 5;
}

.step-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.step-description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
}

.step-visual {
  width: 80px;
  height: 80px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.step-visual i {
  font-size: var(--font-size-2xl);
  color: var(--color-primary);
}

/* Pricing Section */
.pricing {
  padding: var(--space-24) 0;
  background: linear-gradient(135deg, var(--color-neutral-50), var(--color-neutral-100));
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.pricing-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  position: relative;
  transition: all var(--transition-base);
}

.pricing-card.featured {
  background: var(--glass-bg-strong);
  border-color: var(--color-primary);
  transform: scale(1.05);
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.pricing-card.featured:hover {
  transform: scale(1.05) translateY(-8px);
}

.pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: var(--color-neutral-900);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.pricing-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.pricing-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.pricing-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--space-1);
  margin-bottom: var(--space-4);
}

.currency {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.amount {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.period {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.pricing-description {
  color: var(--color-text-secondary);
  text-align: center;
}

.pricing-features {
  margin-bottom: var(--space-8);
}

.pricing-features .feature {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  color: var(--color-text-secondary);
}

.pricing-features .feature i {
  color: var(--color-success);
}

.btn-full {
  width: 100%;
  justify-content: center;
}

/* CTA Section */
.cta {
  padding: var(--space-24) 0;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.cta-content {
  text-align: center;
  position: relative;
  z-index: 5;
}

.cta-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: white;
  margin-bottom: var(--space-6);
}

.cta-description {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.cta .btn-primary {
  background: white;
  color: var(--color-primary);
  border-color: white;
}

.cta .btn-primary:hover {
  background: var(--color-neutral-100);
  transform: translateY(-2px);
}

.cta .btn-outline {
  background: transparent;
  color: white;
  border-color: white;
}

.cta .btn-outline:hover {
  background: white;
  color: var(--color-primary);
}

/* Footer */
.footer {
  background: var(--color-neutral-900);
  color: var(--color-neutral-300);
  padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-16);
  margin-bottom: var(--space-12);
}

.footer-brand .logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  margin-bottom: var(--space-4);
}

.footer-brand .logo i {
  color: var(--color-primary);
  font-size: var(--font-size-2xl);
}

.footer-description {
  line-height: var(--line-height-relaxed);
  max-width: 400px;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.footer-column h4 {
  color: white;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-4);
}

.footer-column a {
  display: block;
  color: var(--color-neutral-400);
  text-decoration: none;
  margin-bottom: var(--space-3);
  transition: color var(--transition-base);
}

.footer-column a:hover {
  color: var(--color-primary);
}

.footer-bottom {
  border-top: 1px solid var(--color-neutral-700);
  padding-top: var(--space-8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-legal {
  display: flex;
  gap: var(--space-6);
}

.footer-legal a {
  color: var(--color-neutral-400);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-base);
}

.footer-legal a:hover {
  color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }

  .hero-visual {
    order: -1;
  }

  .floating-cards {
    display: none;
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .section-title {
    font-size: var(--font-size-3xl);
  }

  .features-grid,
  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .step::after {
    display: none;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-title {
    font-size: var(--font-size-3xl);
  }
}

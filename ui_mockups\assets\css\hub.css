/* Hub-specific styles */

/* Preloader */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-neutral-900), var(--color-neutral-800));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: opacity 0.5s ease-in-out;
}

.preloader.loaded {
  opacity: 0;
  pointer-events: none;
}

.loader {
  width: 48px;
  height: 48px;
  position: relative;
  animation: rotation 1s linear infinite;
}

.loader::after,
.loader::before {
  content: '';
  position: absolute;
  width: 24px;
  height: 24px;
  top: 50%;
  left: 50%;
  transform: scale(0.5) translate(0, 0);
  background-color: var(--color-primary);
  border-radius: 50%;
  animation: animloader 1s infinite ease-in-out;
}

.loader::before {
  background-color: var(--color-secondary);
  transform: scale(0.5) translate(-48px, -48px);
}

@keyframes rotation {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes animloader {
  50% { transform: scale(1) translate(-50%, -50%); }
}

/* Hub Container */
.hub-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-neutral-50), var(--color-neutral-100));
  position: relative;
  overflow-x: hidden;
}

.hub-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100vh;
  background: 
    radial-gradient(circle at 20% 20%, var(--color-primary-alpha) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--color-secondary-alpha) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(6, 234, 175, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Header */
.hub-header {
  position: relative;
  z-index: 10;
  padding: var(--space-6) 0;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.logo i {
  color: var(--color-primary);
  font-size: var(--font-size-3xl);
}

.tagline {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-left: calc(var(--font-size-3xl) + var(--space-3));
}

.header-actions {
  display: flex;
  gap: var(--space-3);
}

/* Main Content */
.hub-main {
  position: relative;
  z-index: 5;
  padding: var(--space-12) 0;
}

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: var(--space-16);
  padding: 0 var(--space-4);
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-6);
  background: linear-gradient(135deg, var(--color-text-primary), var(--color-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  max-width: 600px;
  margin: 0 auto;
}

/* Mockups Grid */
.mockups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  margin-bottom: var(--space-16);
}

.mockup-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.mockup-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
}

.mockup-card:hover {
  transform: translateY(-8px);
  background: var(--glass-bg-strong);
  box-shadow: var(--shadow-2xl);
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.card-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
}

.card-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-4);
}

.card-icon i {
  font-size: var(--font-size-xl);
  color: var(--color-neutral-900);
}

.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-ready {
  background: var(--color-success);
  color: white;
}

.status-progress {
  background: var(--color-warning);
  color: white;
}

.status-planned {
  background: var(--color-neutral-400);
  color: white;
}

.card-content {
  margin-bottom: var(--space-6);
}

.card-content p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
  line-height: var(--line-height-relaxed);
}

.feature-list {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.feature-list li {
  background: var(--color-primary-alpha);
  color: var(--color-primary-dark);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.card-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.card-actions .btn {
  flex: 1;
}

.card-actions .btn-secondary {
  flex: none;
  width: 44px;
  height: 44px;
  padding: 0;
}

/* Design System Section */
.design-system-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  margin-bottom: var(--space-16);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.section-header h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.section-header p {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.design-system-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.design-system-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  text-decoration: none;
  color: inherit;
  transition: all var(--transition-base);
  text-align: center;
}

.design-system-card:hover {
  transform: translateY(-4px);
  background: var(--glass-bg-strong);
  box-shadow: var(--shadow-lg);
}

.design-system-card .card-icon {
  margin: 0 auto var(--space-4);
}

.design-system-card h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
}

.design-system-card p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Footer */
.hub-footer {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-top: 1px solid var(--glass-border);
  padding: var(--space-8) 0;
  position: relative;
  z-index: 10;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-content p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.footer-links {
  display: flex;
  gap: var(--space-6);
}

.footer-links a {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color var(--transition-base);
}

.footer-links a:hover {
  color: var(--color-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-4xl);
  }
  
  .hero-description {
    font-size: var(--font-size-lg);
  }
  
  .mockups-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .mockup-card {
    padding: var(--space-6);
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .tagline {
    margin-left: 0;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
}

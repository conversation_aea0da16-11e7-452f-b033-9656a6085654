@echo off
setlocal enabledelayedexpansion

REM UniversalWallet UI Mockups - Modern Development Environment Setup (Windows)
REM This script sets up the modern development environment with Vite and modern tools

echo 🚀 Setting up UniversalWallet UI Mockups - Modern Development Environment
echo ==================================================================

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    echo    Visit: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js detected
for /f "tokens=1 delims=." %%a in ('node --version') do set NODE_MAJOR=%%a
set NODE_MAJOR=%NODE_MAJOR:v=%
if %NODE_MAJOR% LSS 18 (
    echo ❌ Node.js version 18+ is required. Current version: 
    node --version
    echo    Please update Node.js: https://nodejs.org/
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ package.json not found. Please run this script from the ui_mockups directory.
    pause
    exit /b 1
)

echo ✅ Found package.json

REM Install dependencies
echo.
echo 📦 Installing dependencies...
echo ================================

REM Check for package managers and use the best available
where pnpm >nul 2>&1
if not errorlevel 1 (
    echo Using pnpm...
    pnpm install
    goto :install_done
)

where yarn >nul 2>&1
if not errorlevel 1 (
    echo Using yarn...
    yarn install
    goto :install_done
)

echo Using npm...
npm install

:install_done
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo.
    echo 📝 Creating .env file...
    (
        echo # UniversalWallet UI Mockups Environment Variables
        echo NODE_ENV=development
        echo VITE_APP_NAME=UniversalWallet UI Mockups
        echo VITE_APP_VERSION=1.0.0
        echo VITE_DEV_PORT=8000
    ) > .env
    echo ✅ Created .env file
)

REM Run initial linting and formatting
echo.
echo 🔧 Running code quality checks...
echo ==================================

echo Running ESLint...
where pnpm >nul 2>&1
if not errorlevel 1 (
    pnpm run lint --fix
    goto :lint_done
)

where yarn >nul 2>&1
if not errorlevel 1 (
    yarn lint --fix
    goto :lint_done
)

npm run lint -- --fix

:lint_done

echo Running Prettier...
where pnpm >nul 2>&1
if not errorlevel 1 (
    pnpm run format
    goto :format_done
)

where yarn >nul 2>&1
if not errorlevel 1 (
    yarn format
    goto :format_done
)

npm run format

:format_done
echo ✅ Code quality checks completed

REM Success message
echo.
echo 🎉 Setup completed successfully!
echo ================================
echo.
echo 🚀 To start the development server:
echo    npm run dev
echo    # or
echo    yarn dev
echo    # or
echo    pnpm dev
echo.
echo 📖 Available commands:
echo    npm run dev      - Start development server
echo    npm run build    - Build for production
echo    npm run preview  - Preview production build
echo    npm run lint     - Run ESLint
echo    npm run format   - Format code with Prettier
echo.
echo 🌐 The server will be available at: http://localhost:8000
echo.
echo 📚 For more information, see README-DEV.md
echo.
echo Happy coding! 🎨✨
echo.
pause

{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "globals": {"gsap": "readonly", "ScrollTrigger": "readonly", "__APP_VERSION__": "readonly", "__BUILD_DATE__": "readonly"}, "rules": {"indent": ["error", 2], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["error", "always"], "no-unused-vars": ["warn"], "no-console": ["warn"], "no-debugger": ["warn"], "prefer-const": ["error"], "no-var": ["error"], "arrow-spacing": ["error"], "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "never"], "comma-dangle": ["error", "never"], "eol-last": ["error", "always"], "no-trailing-spaces": ["error"], "space-before-blocks": ["error"], "keyword-spacing": ["error"], "space-infix-ops": ["error"]}, "ignorePatterns": ["dist/", "node_modules/", "*.min.js"]}
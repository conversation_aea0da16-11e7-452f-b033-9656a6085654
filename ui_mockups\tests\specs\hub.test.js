// Hub Page Tests
const { test, expect } = require('@playwright/test');
const { waitForAnimations, hasGlassmorphism, checkAccessibility } = require('../utils/test-helpers');

test.describe('UniversalWallet Hub Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await waitForAnimations(page);
  });

  test('should load hub page successfully @desktop @smoke', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/UniversalWallet.*UI Mockups Hub/);
    
    // Check main elements are visible
    await expect(page.locator('.hub-header')).toBeVisible();
    await expect(page.locator('.hero-section')).toBeVisible();
    await expect(page.locator('.mockups-grid')).toBeVisible();
    
    // Check logo and branding
    await expect(page.locator('.logo')).toBeVisible();
    await expect(page.locator('.logo i.fa-wallet')).toBeVisible();
    await expect(page.locator('.logo span')).toHaveText('UniversalWallet');
  });

  test('should have working theme toggle @desktop @interactions', async ({ page }) => {
    const themeButton = page.locator('[onclick="toggleTheme()"]');
    await expect(themeButton).toBeVisible();
    
    // Test theme toggle
    const initialTheme = await page.getAttribute('html', 'data-theme');
    await themeButton.click();
    await page.waitForTimeout(500);
    
    const newTheme = await page.getAttribute('html', 'data-theme');
    expect(newTheme).not.toBe(initialTheme);
    
    // Check icon changes
    const icon = themeButton.locator('i');
    const iconClass = await icon.getAttribute('class');
    expect(iconClass).toMatch(/fa-(sun|moon)/);
  });

  test('should display all mockup cards @desktop @content', async ({ page }) => {
    const expectedCards = [
      'Marketing Website',
      'Individual Dashboard', 
      'Business Dashboard',
      'Merchant Dashboard',
      'Admin Dashboard',
      'Mobile App UI'
    ];
    
    for (const cardTitle of expectedCards) {
      const card = page.locator('.mockup-card', { hasText: cardTitle });
      await expect(card).toBeVisible();
      
      // Check card structure
      await expect(card.locator('.card-header h3')).toHaveText(cardTitle);
      await expect(card.locator('.status-badge')).toBeVisible();
      await expect(card.locator('.btn-primary')).toBeVisible();
    }
  });

  test('should have working mockup navigation links @desktop @navigation', async ({ page }) => {
    const mockupLinks = [
      { text: 'Marketing Website', href: 'marketing/index.html' },
      { text: 'Individual Dashboard', href: 'individual/index.html' },
      { text: 'Business Dashboard', href: 'business/index.html' },
      { text: 'Mobile App UI', href: 'mobile/index.html' }
    ];
    
    for (const link of mockupLinks) {
      const card = page.locator('.mockup-card', { hasText: link.text });
      const viewButton = card.locator('.btn-primary');
      
      await expect(viewButton).toBeVisible();
      await expect(viewButton).toHaveAttribute('href', link.href);
    }
  });

  test('should have glassmorphism effects @desktop @visual', async ({ page }) => {
    // Check header glassmorphism
    const header = page.locator('.hub-header');
    expect(await hasGlassmorphism(header)).toBe(true);
    
    // Check mockup cards glassmorphism
    const cards = page.locator('.mockup-card');
    const cardCount = await cards.count();
    
    for (let i = 0; i < cardCount; i++) {
      const card = cards.nth(i);
      expect(await hasGlassmorphism(card)).toBe(true);
    }
  });

  test('should have hover effects on cards @desktop @interactions', async ({ page }) => {
    const firstCard = page.locator('.mockup-card').first();
    
    // Get initial transform
    const initialTransform = await firstCard.evaluate(el => 
      window.getComputedStyle(el).transform
    );
    
    // Hover over card
    await firstCard.hover();
    await page.waitForTimeout(500);
    
    // Check transform changed (hover effect)
    const hoverTransform = await firstCard.evaluate(el => 
      window.getComputedStyle(el).transform
    );
    
    expect(hoverTransform).not.toBe(initialTransform);
  });

  test('should display design system section @desktop @content', async ({ page }) => {
    const designSystemSection = page.locator('.design-system-section');
    await expect(designSystemSection).toBeVisible();
    
    // Check section header
    await expect(designSystemSection.locator('h2')).toHaveText('Design System');
    
    // Check design system cards
    const expectedCards = ['Components Library', 'Color System', 'Typography'];
    
    for (const cardTitle of expectedCards) {
      const card = page.locator('.design-system-card', { hasText: cardTitle });
      await expect(card).toBeVisible();
    }
  });

  test('should have working modal functionality @desktop @interactions', async ({ page }) => {
    // Test "About" modal
    const aboutLink = page.locator('[onclick="showInfo()"]');
    await aboutLink.click();
    
    // Check modal appears
    const modal = page.locator('.modal-overlay');
    await expect(modal).toBeVisible();
    await expect(modal).toHaveClass(/active/);
    
    // Check modal content
    await expect(modal.locator('.modal-header h2')).toHaveText('About UniversalWallet UI Mockups');
    
    // Close modal
    const closeButton = modal.locator('.modal-close');
    await closeButton.click();
    await page.waitForTimeout(500);
    
    await expect(modal).not.toHaveClass(/active/);
  });

  test('should be responsive @mobile @responsive', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    // Check mobile layout
    await expect(page.locator('.hub-header')).toBeVisible();
    await expect(page.locator('.mockups-grid')).toBeVisible();
    
    // Check cards stack vertically on mobile
    const cards = page.locator('.mockup-card');
    const firstCard = cards.first();
    const secondCard = cards.nth(1);
    
    const firstCardBox = await firstCard.boundingBox();
    const secondCardBox = await secondCard.boundingBox();
    
    // Second card should be below first card on mobile
    expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 10);
  });

  test('should have accessible navigation @desktop @accessibility', async ({ page }) => {
    const accessibility = await checkAccessibility(page);
    
    // Should have ARIA elements
    expect(accessibility.ariaElements).toBeGreaterThan(0);
    
    // Should have focusable elements
    expect(accessibility.focusableElements).toBeGreaterThan(0);
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    const focusedElement = await page.locator(':focus').count();
    expect(focusedElement).toBe(1);
  });

  test('should have working external link buttons @desktop @interactions', async ({ page }) => {
    const externalButtons = page.locator('.btn-secondary[onclick*="openInNewTab"]');
    const buttonCount = await externalButtons.count();
    
    expect(buttonCount).toBeGreaterThan(0);
    
    // Test first external button
    const firstButton = externalButtons.first();
    await expect(firstButton).toBeVisible();
    
    // Click should not navigate away (since it opens new tab)
    await firstButton.click();
    await page.waitForTimeout(500);
    
    // Should still be on hub page
    expect(page.url()).toContain('/');
  });

  test('should load without JavaScript errors @desktop @smoke', async ({ page }) => {
    const errors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.reload();
    await waitForAnimations(page);
    
    // Should have no JavaScript errors
    expect(errors).toHaveLength(0);
  });

  test('should have proper footer @desktop @content', async ({ page }) => {
    const footer = page.locator('.hub-footer');
    await expect(footer).toBeVisible();
    
    // Check footer content
    await expect(footer.locator('p')).toContainText('2024 UniversalWallet');
    
    // Check footer links
    const footerLinks = footer.locator('.footer-links a');
    await expect(footerLinks).toHaveCount(2);
  });
});

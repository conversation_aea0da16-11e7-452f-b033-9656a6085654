// Mobile App JavaScript

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeMobileApp();
    setupTouchInteractions();
    setupAnimations();
    setupNavigation();
});

// Initialize Mobile App
function initializeMobileApp() {
    // Set viewport height for mobile browsers
    setViewportHeight();
    
    // Initialize touch feedback
    setupTouchFeedback();
    
    // Setup balance toggle
    setupBalanceToggle();
    
    // Setup notification overlay
    setupNotificationOverlay();
    
    // Initialize time display
    updateTime();
    setInterval(updateTime, 1000);
    
    // Prevent default touch behaviors
    preventDefaultTouchBehaviors();
    
    // Start mobile-specific updates
    startMobileUpdates();
}

// Set Viewport Height
function setViewportHeight() {
    const setVH = () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    
    setVH();
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', setVH);
}

// Setup Touch Interactions
function setupTouchInteractions() {
    // Action buttons
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('touchstart', handleTouchStart);
        btn.addEventListener('touchend', handleTouchEnd);
        btn.addEventListener('click', handleActionClick);
    });
    
    // Service cards
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('touchstart', handleTouchStart);
        card.addEventListener('touchend', handleTouchEnd);
        card.addEventListener('click', handleServiceClick);
    });
    
    // Transaction items
    const transactionItems = document.querySelectorAll('.transaction-item');
    transactionItems.forEach(item => {
        item.addEventListener('touchstart', handleTouchStart);
        item.addEventListener('touchend', handleTouchEnd);
        item.addEventListener('click', handleTransactionClick);
    });
    
    // Header buttons
    const headerBtns = document.querySelectorAll('.header-btn');
    headerBtns.forEach(btn => {
        btn.addEventListener('touchstart', handleTouchStart);
        btn.addEventListener('touchend', handleTouchEnd);
    });
    
    // Bottom navigation
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('touchstart', handleTouchStart);
        item.addEventListener('touchend', handleTouchEnd);
        item.addEventListener('click', handleNavClick);
    });
}

// Handle Touch Start
function handleTouchStart(e) {
    this.classList.add('touching');
    
    // Add haptic feedback simulation
    if (navigator.vibrate) {
        navigator.vibrate(10);
    }
    
    // Scale animation
    gsap.to(this, {
        duration: 0.1,
        scale: 0.95,
        ease: 'power2.out'
    });
}

// Handle Touch End
function handleTouchEnd(e) {
    this.classList.remove('touching');
    
    // Reset scale
    gsap.to(this, {
        duration: 0.2,
        scale: 1,
        ease: 'power2.out'
    });
}

// Setup Touch Feedback
function setupTouchFeedback() {
    const touchFeedback = document.getElementById('touch-feedback');
    
    document.addEventListener('touchstart', function(e) {
        const touch = e.touches[0];
        const x = touch.clientX;
        const y = touch.clientY;
        
        touchFeedback.style.left = (x - 20) + 'px';
        touchFeedback.style.top = (y - 20) + 'px';
        touchFeedback.classList.add('active');
        
        setTimeout(() => {
            touchFeedback.classList.remove('active');
        }, 600);
    });
}

// Setup Animations
function setupAnimations() {
    // Animate app entrance
    gsap.from('.mobile-app', {
        duration: 0.8,
        scale: 0.9,
        opacity: 0,
        ease: 'power3.out'
    });
    
    // Animate status bar
    gsap.from('.status-bar', {
        duration: 0.6,
        y: -20,
        opacity: 0,
        ease: 'power3.out',
        delay: 0.2
    });
    
    // Animate header
    gsap.from('.app-header', {
        duration: 0.6,
        y: -30,
        opacity: 0,
        ease: 'power3.out',
        delay: 0.3
    });
    
    // Animate balance card
    gsap.from('.balance-card', {
        duration: 0.8,
        y: 40,
        opacity: 0,
        ease: 'power3.out',
        delay: 0.4
    });
    
    // Animate quick actions
    gsap.from('.action-btn', {
        duration: 0.6,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 0.6
    });
    
    // Animate services
    gsap.from('.service-card', {
        duration: 0.6,
        x: -30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 0.8
    });
    
    // Animate transactions
    gsap.from('.transaction-item', {
        duration: 0.6,
        x: -30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 1
    });
    
    // Animate bottom navigation
    gsap.from('.bottom-nav', {
        duration: 0.6,
        y: 100,
        opacity: 0,
        ease: 'power3.out',
        delay: 1.2
    });
    
    // Animate balance numbers
    animateMobileCounters();
}

// Setup Navigation
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Get page name
            const page = this.dataset.page;
            
            // Handle navigation
            handleNavigation(page);
        });
    });
}

// Handle Navigation
function handleNavigation(page) {
    console.log(`Navigating to: ${page}`);
    
    // Simulate page transition
    gsap.to('.app-main', {
        duration: 0.3,
        opacity: 0.5,
        scale: 0.95,
        ease: 'power2.out',
        onComplete: () => {
            // Simulate loading new content
            setTimeout(() => {
                gsap.to('.app-main', {
                    duration: 0.3,
                    opacity: 1,
                    scale: 1,
                    ease: 'power2.out'
                });
            }, 200);
        }
    });
    
    // Show loading feedback
    showMobileNotification(`Loading ${page}...`, 'info');
}

// Handle Action Click
function handleActionClick(e) {
    const action = this.dataset.action;
    console.log(`Action: ${action}`);
    
    // Add loading state
    const icon = this.querySelector('.action-icon i');
    const originalIcon = icon.className;
    
    icon.className = 'fas fa-spinner fa-spin';
    
    setTimeout(() => {
        icon.className = originalIcon;
        showMobileNotification(`${action} action completed!`, 'success');
    }, 1500);
}

// Handle Service Click
function handleServiceClick(e) {
    const serviceTitle = this.querySelector('.service-title').textContent;
    console.log(`Service: ${serviceTitle}`);
    
    showMobileNotification(`Opening ${serviceTitle}...`, 'info');
}

// Handle Transaction Click
function handleTransactionClick(e) {
    const transactionTitle = this.querySelector('.transaction-title').textContent;
    console.log(`Transaction: ${transactionTitle}`);
    
    showMobileNotification(`Transaction details for ${transactionTitle}`, 'info');
}

// Handle Nav Click
function handleNavClick(e) {
    // Handled in setupNavigation
}

// Setup Balance Toggle
function setupBalanceToggle() {
    const balanceToggle = document.querySelector('.balance-toggle');
    let balanceVisible = true;
    
    if (balanceToggle) {
        balanceToggle.addEventListener('click', function() {
            balanceVisible = !balanceVisible;
            
            const amounts = document.querySelectorAll('.amount, .chip-amount');
            const icon = this.querySelector('i');
            
            amounts.forEach(amount => {
                if (balanceVisible) {
                    amount.style.filter = 'none';
                    icon.className = 'fas fa-eye';
                } else {
                    amount.style.filter = 'blur(6px)';
                    icon.className = 'fas fa-eye-slash';
                }
            });
        });
    }
}

// Setup Notification Overlay
function setupNotificationOverlay() {
    const notificationsBtn = document.getElementById('notifications-btn');
    const notificationsOverlay = document.getElementById('notifications-overlay');
    const closeOverlay = document.querySelector('.close-overlay');
    
    if (notificationsBtn && notificationsOverlay) {
        notificationsBtn.addEventListener('click', function() {
            notificationsOverlay.classList.add('active');
        });
        
        closeOverlay.addEventListener('click', function() {
            notificationsOverlay.classList.remove('active');
        });
        
        // Close on swipe down (simplified)
        let startY = 0;
        notificationsOverlay.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
        });
        
        notificationsOverlay.addEventListener('touchmove', function(e) {
            const currentY = e.touches[0].clientY;
            const diff = currentY - startY;
            
            if (diff > 100) {
                this.classList.remove('active');
            }
        });
    }
}

// Update Time
function updateTime() {
    const timeElement = document.querySelector('.time');
    if (timeElement) {
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        timeElement.textContent = `${hours}:${minutes}`;
    }
}

// Prevent Default Touch Behaviors
function preventDefaultTouchBehaviors() {
    // Prevent pull-to-refresh
    document.body.addEventListener('touchstart', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    }, { passive: false });
    
    document.body.addEventListener('touchend', function(e) {
        if (e.touches.length > 0) {
            e.preventDefault();
        }
    }, { passive: false });
    
    document.body.addEventListener('touchmove', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    }, { passive: false });
    
    // Prevent zoom
    document.addEventListener('gesturestart', function(e) {
        e.preventDefault();
    });
    
    document.addEventListener('gesturechange', function(e) {
        e.preventDefault();
    });
    
    document.addEventListener('gestureend', function(e) {
        e.preventDefault();
    });
}

// Animate Mobile Counters
function animateMobileCounters() {
    const counters = document.querySelectorAll('.amount, .chip-amount');
    
    counters.forEach(counter => {
        const target = parseFloat(counter.textContent.replace(/[^\d.]/g, ''));
        const prefix = counter.textContent.match(/[^\d.]/g)?.join('') || '';
        
        gsap.to({ value: 0 }, {
            duration: 2,
            value: target,
            ease: 'power2.out',
            onUpdate: function() {
                const currentValue = this.targets()[0].value;
                counter.textContent = prefix + currentValue.toFixed(2);
            },
            delay: 0.8
        });
    });
}

// Start Mobile Updates
function startMobileUpdates() {
    // Simulate real-time balance updates
    setInterval(() => {
        updateMobileBalances();
    }, 30000);
    
    // Simulate new notifications
    setInterval(() => {
        addNewNotification();
    }, 60000);
}

// Update Mobile Balances
function updateMobileBalances() {
    const balanceElements = document.querySelectorAll('.amount, .chip-amount');
    
    balanceElements.forEach(element => {
        const currentValue = parseFloat(element.textContent.replace(/[^\d.]/g, ''));
        const change = (Math.random() - 0.5) * 5; // Small random changes
        const newValue = Math.max(0, currentValue + change);
        const prefix = element.textContent.match(/[^\d.]/g)?.join('') || '';
        
        gsap.to({ value: currentValue }, {
            duration: 1,
            value: newValue,
            ease: 'power2.out',
            onUpdate: function() {
                element.textContent = prefix + this.targets()[0].value.toFixed(2);
            }
        });
    });
}

// Add New Notification
function addNewNotification() {
    const notificationDot = document.querySelector('.notification-dot');
    if (notificationDot) {
        // Animate notification dot
        gsap.to(notificationDot, {
            duration: 0.3,
            scale: 1.5,
            ease: 'power2.out',
            yoyo: true,
            repeat: 1
        });
    }
}

// Show Mobile Notification
function showMobileNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `mobile-toast mobile-toast-${type}`;
    notification.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add toast styles if not exists
    if (!document.querySelector('#mobile-toast-styles')) {
        const styles = document.createElement('style');
        styles.id = 'mobile-toast-styles';
        styles.textContent = `
            .mobile-toast {
                position: fixed;
                top: 60px;
                left: 20px;
                right: 20px;
                background: var(--glass-bg-strong);
                backdrop-filter: var(--glass-backdrop);
                border: 1px solid var(--glass-border);
                border-radius: var(--radius-lg);
                padding: var(--space-4);
                z-index: 9999;
                transform: translateY(-100px);
                opacity: 0;
                transition: all var(--transition-base);
            }
            
            .mobile-toast.show {
                transform: translateY(0);
                opacity: 1;
            }
            
            .mobile-toast-success {
                border-left: 4px solid var(--color-success);
            }
            
            .mobile-toast-info {
                border-left: 4px solid var(--color-primary);
            }
            
            .toast-content {
                display: flex;
                align-items: center;
                gap: var(--space-2);
                color: var(--color-text-primary);
                font-size: var(--font-size-sm);
                font-weight: var(--font-weight-medium);
            }
            
            .mobile-toast-success .toast-content i {
                color: var(--color-success);
            }
            
            .mobile-toast-info .toast-content i {
                color: var(--color-primary);
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Auto hide after 2 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 2000);
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('Mobile App Error:', e.error);
    showMobileNotification('An error occurred', 'info');
});

// Orientation change handler
window.addEventListener('orientationchange', function() {
    setTimeout(() => {
        setViewportHeight();
    }, 100);
});

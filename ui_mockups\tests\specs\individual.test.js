// Individual Dashboard Tests
const { test, expect } = require('@playwright/test');
const { waitForAnimations, hasGlassmorphism, testLoadingState, waitForStable } = require('../utils/test-helpers');

test.describe('Individual Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/individual/');
    await waitForAnimations(page);
  });

  test('should load individual dashboard successfully @desktop @smoke', async ({ page }) => {
    await expect(page).toHaveTitle(/UniversalWallet.*Personal Dashboard/);
    
    // Check main layout elements
    await expect(page.locator('.sidebar')).toBeVisible();
    await expect(page.locator('.main-content')).toBeVisible();
    await expect(page.locator('.top-header')).toBeVisible();
    await expect(page.locator('.page-content')).toBeVisible();
  });

  test('should have working sidebar navigation @desktop @navigation', async ({ page }) => {
    const menuItems = page.locator('.menu-item');
    
    // Check menu items are present
    const expectedMenuItems = [
      'Dashboard', 'My Accounts', 'Transfers', 'Bill Payments',
      'Savings Groups', 'Virtual Cards', 'Analytics', 'Transactions', 'Settings'
    ];
    
    await expect(menuItems).toHaveCount(expectedMenuItems.length);
    
    // Test navigation
    for (let i = 1; i < Math.min(expectedMenuItems.length, 4); i++) {
      const menuItem = menuItems.nth(i);
      await menuItem.click();
      await page.waitForTimeout(500);
      
      // Check active state
      await expect(menuItem).toHaveClass(/active/);
      
      // Check page title updates
      const pageTitle = page.locator('#page-title');
      await expect(pageTitle).not.toHaveText('Dashboard');
    }
  });

  test('should have collapsible sidebar @desktop @interactions', async ({ page }) => {
    const sidebar = page.locator('.sidebar');
    const sidebarToggle = page.locator('.sidebar-toggle');
    
    // Test sidebar collapse
    await sidebarToggle.click();
    await page.waitForTimeout(500);
    
    await expect(sidebar).toHaveClass(/collapsed/);
    
    // Test sidebar expand
    await sidebarToggle.click();
    await page.waitForTimeout(500);
    
    await expect(sidebar).not.toHaveClass(/collapsed/);
  });

  test('should display balance overview correctly @desktop @content', async ({ page }) => {
    const balanceSection = page.locator('.balance-overview');
    await expect(balanceSection).toBeVisible();
    
    const balanceCards = balanceSection.locator('.balance-card');
    await expect(balanceCards).toHaveCount(3);
    
    // Check primary balance card
    const primaryCard = balanceCards.first();
    await expect(primaryCard).toHaveClass(/primary/);
    await expect(primaryCard.locator('.balance-title')).toHaveText('Total Balance');
    await expect(primaryCard.locator('.amount')).toBeVisible();
    await expect(primaryCard.locator('.balance-change')).toBeVisible();
    
    // Check balance toggle
    const balanceToggle = primaryCard.locator('.balance-toggle');
    await expect(balanceToggle).toBeVisible();
  });

  test('should have working balance toggle @desktop @interactions', async ({ page }) => {
    const balanceToggle = page.locator('.balance-toggle').first();
    const amounts = page.locator('.amount, .balance-value');
    
    // Get initial state
    const firstAmount = amounts.first();
    const initialFilter = await firstAmount.evaluate(el => 
      window.getComputedStyle(el).filter
    );
    
    // Toggle balance visibility
    await balanceToggle.click();
    await page.waitForTimeout(500);
    
    // Check if filter changed (blur effect)
    const newFilter = await firstAmount.evaluate(el => 
      window.getComputedStyle(el).filter
    );
    
    expect(newFilter).not.toBe(initialFilter);
    
    // Check icon changed
    const icon = balanceToggle.locator('i');
    const iconClass = await icon.getAttribute('class');
    expect(iconClass).toContain('fa-eye-slash');
  });

  test('should display quick actions @desktop @content', async ({ page }) => {
    const quickActions = page.locator('.quick-actions');
    await expect(quickActions).toBeVisible();
    
    const actionCards = quickActions.locator('.action-card');
    await expect(actionCards).toHaveCount(6);
    
    const expectedActions = [
      'Send Money', 'Pay Bills', 'Request Money', 
      'Scan QR', 'Top Up', 'Savings'
    ];
    
    for (let i = 0; i < expectedActions.length; i++) {
      const card = actionCards.nth(i);
      await expect(card.locator('.action-title')).toHaveText(expectedActions[i]);
      await expect(card.locator('.action-icon')).toBeVisible();
    }
  });

  test('should have working quick action buttons @desktop @interactions', async ({ page }) => {
    const actionCards = page.locator('.action-card');
    const firstAction = actionCards.first();
    
    // Test loading state
    const loadingResult = await testLoadingState(page, '.action-card[data-action="send-money"]');
    expect(loadingResult.hasLoadingIndicator).toBe(true);
  });

  test('should display connected accounts @desktop @content', async ({ page }) => {
    const accountsSection = page.locator('.connected-accounts');
    await expect(accountsSection).toBeVisible();
    
    const accountCards = accountsSection.locator('.account-card');
    await expect(accountCards).toHaveCount(3);
    
    const expectedAccounts = ['EcoCash', 'OneMoney', 'ZB Bank'];
    
    for (let i = 0; i < expectedAccounts.length; i++) {
      const card = accountCards.nth(i);
      await expect(card.locator('.provider-name')).toContainText(expectedAccounts[i]);
      await expect(card.locator('.provider-icon')).toBeVisible();
      await expect(card.locator('.balance-value')).toBeVisible();
      await expect(card.locator('.account-status.connected')).toBeVisible();
      
      // Check action buttons
      const actionButtons = card.locator('.btn');
      await expect(actionButtons).toHaveCount(3);
    }
  });

  test('should display recent transactions @desktop @content', async ({ page }) => {
    const transactionsSection = page.locator('.recent-transactions');
    await expect(transactionsSection).toBeVisible();
    
    const transactionItems = transactionsSection.locator('.transaction-item');
    await expect(transactionItems).toHaveCount(4);
    
    // Check transaction structure
    for (let i = 0; i < Math.min(await transactionItems.count(), 2); i++) {
      const item = transactionItems.nth(i);
      await expect(item.locator('.transaction-icon')).toBeVisible();
      await expect(item.locator('.transaction-title')).toBeVisible();
      await expect(item.locator('.transaction-subtitle')).toBeVisible();
      await expect(item.locator('.transaction-amount')).toBeVisible();
      await expect(item.locator('.transaction-status')).toBeVisible();
    }
  });

  test('should have working header actions @desktop @interactions', async ({ page }) => {
    const headerActions = page.locator('.header-actions .action-btn');
    
    // Check notification button
    const notificationBtn = headerActions.first();
    await expect(notificationBtn).toBeVisible();
    await expect(notificationBtn.locator('.notification-badge')).toBeVisible();
    
    // Test header button clicks
    for (let i = 0; i < Math.min(await headerActions.count(), 3); i++) {
      const button = headerActions.nth(i);
      await button.click();
      await page.waitForTimeout(300);
    }
  });

  test('should have glassmorphism effects @desktop @visual', async ({ page }) => {
    // Check sidebar glassmorphism
    const sidebar = page.locator('.sidebar');
    expect(await hasGlassmorphism(sidebar)).toBe(true);
    
    // Check header glassmorphism
    const header = page.locator('.top-header');
    expect(await hasGlassmorphism(header)).toBe(true);
    
    // Check balance cards glassmorphism
    const balanceCards = page.locator('.balance-card:not(.primary)');
    if (await balanceCards.count() > 0) {
      expect(await hasGlassmorphism(balanceCards.first())).toBe(true);
    }
  });

  test('should be responsive @mobile @responsive', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    // Check mobile sidebar behavior
    const sidebar = page.locator('.sidebar');
    const mobileSidebarToggle = page.locator('.mobile-sidebar-toggle');
    
    await expect(mobileSidebarToggle).toBeVisible();
    
    // Test mobile sidebar toggle
    await mobileSidebarToggle.click();
    await page.waitForTimeout(500);
    
    await expect(sidebar).toHaveClass(/mobile-open/);
    
    // Test closing sidebar by clicking outside
    await page.locator('.main-content').click();
    await page.waitForTimeout(500);
    
    await expect(sidebar).not.toHaveClass(/mobile-open/);
  });

  test('should have working user profile @desktop @interactions', async ({ page }) => {
    const userProfile = page.locator('.user-profile');
    await expect(userProfile).toBeVisible();
    
    // Check user info
    await expect(userProfile.locator('.user-name')).toHaveText('John Doe');
    await expect(userProfile.locator('.user-status')).toHaveText('Personal Account');
    await expect(userProfile.locator('.user-avatar img')).toBeVisible();
    
    // Test user menu toggle
    const userMenuToggle = userProfile.locator('.user-menu-toggle');
    await userMenuToggle.click();
    await page.waitForTimeout(300);
  });

  test('should handle real-time updates @desktop @functionality', async ({ page }) => {
    // Wait for initial load
    await page.waitForTimeout(2000);
    
    // Check if balance values are being updated
    const balanceAmount = page.locator('.balance-amount .amount').first();
    const initialValue = await balanceAmount.textContent();
    
    // Wait for potential updates (simulated in the app)
    await page.waitForTimeout(5000);
    
    // Note: Real-time updates are simulated, so we just verify the elements exist
    await expect(balanceAmount).toBeVisible();
    await expect(balanceAmount).not.toHaveText('');
  });

  test('should have working transaction interactions @desktop @interactions', async ({ page }) => {
    const transactionItems = page.locator('.transaction-item');
    
    if (await transactionItems.count() > 0) {
      const firstTransaction = transactionItems.first();
      
      // Test hover effect
      await firstTransaction.hover();
      await page.waitForTimeout(300);
      
      // Test click interaction
      await firstTransaction.click();
      await page.waitForTimeout(300);
    }
  });

  test('should load without JavaScript errors @desktop @smoke', async ({ page }) => {
    const errors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.reload();
    await waitForAnimations(page);
    
    expect(errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_')
    )).toHaveLength(0);
  });

  test('should have proper page transitions @desktop @animations', async ({ page }) => {
    const menuItems = page.locator('.menu-item');
    const pageContent = page.locator('.page-content');
    
    // Test page transition
    const accountsMenuItem = page.locator('.menu-item[data-page="accounts"]');
    await accountsMenuItem.click();
    
    // Wait for transition
    await waitForStable(pageContent);
    
    // Check page changed
    const pageTitle = page.locator('#page-title');
    await expect(pageTitle).toHaveText('My Accounts');
  });
});

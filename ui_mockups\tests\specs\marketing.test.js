// Marketing Website Tests
const { test, expect } = require('@playwright/test');
const { waitForAnimations, hasGlassmorphism, checkAnimationPerformance, testResponsive } = require('../utils/test-helpers');

test.describe('Marketing Website', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/marketing/');
    await waitForAnimations(page);
  });

  test('should load marketing page successfully @desktop @smoke', async ({ page }) => {
    await expect(page).toHaveTitle(/UniversalWallet.*Premier Unified Financial Platform/);
    
    // Check main sections
    await expect(page.locator('.navbar')).toBeVisible();
    await expect(page.locator('.hero')).toBeVisible();
    await expect(page.locator('.features')).toBeVisible();
    await expect(page.locator('.how-it-works')).toBeVisible();
    await expect(page.locator('.pricing')).toBeVisible();
    await expect(page.locator('.cta')).toBeVisible();
    await expect(page.locator('.footer')).toBeVisible();
  });

  test('should have working navigation @desktop @navigation', async ({ page }) => {
    const navLinks = page.locator('.nav-link');
    const expectedLinks = ['Features', 'How It Works', 'Pricing', 'About', 'Contact'];
    
    await expect(navLinks).toHaveCount(expectedLinks.length);
    
    // Test navigation links
    for (let i = 0; i < expectedLinks.length; i++) {
      const link = navLinks.nth(i);
      await expect(link).toHaveText(expectedLinks[i]);
      await expect(link).toHaveAttribute('href', new RegExp(`#${expectedLinks[i].toLowerCase().replace(' ', '-')}`));
    }
  });

  test('should have smooth scroll navigation @desktop @animations', async ({ page }) => {
    const featuresLink = page.locator('.nav-link[href="#features"]');
    
    // Click features link
    await featuresLink.click();
    await page.waitForTimeout(1500); // Wait for smooth scroll
    
    // Check if features section is in view
    const featuresSection = page.locator('#features');
    const isInView = await featuresSection.evaluate(el => {
      const rect = el.getBoundingClientRect();
      return rect.top >= 0 && rect.top <= window.innerHeight;
    });
    
    expect(isInView).toBe(true);
  });

  test('should have working hero section @desktop @content', async ({ page }) => {
    const hero = page.locator('.hero');
    
    // Check hero content
    await expect(hero.locator('.hero-title')).toContainText('Unify Your Financial World');
    await expect(hero.locator('.hero-description')).toBeVisible();
    
    // Check hero actions
    const downloadBtn = hero.locator('.btn-primary');
    const demoBtn = hero.locator('.btn-secondary');
    
    await expect(downloadBtn).toBeVisible();
    await expect(downloadBtn).toContainText('Download App');
    await expect(demoBtn).toBeVisible();
    await expect(demoBtn).toContainText('Watch Demo');
    
    // Check hero stats
    const stats = hero.locator('.hero-stats .stat');
    await expect(stats).toHaveCount(3);
    
    const statNumbers = ['500K+', '$50M+', '99.9%'];
    for (let i = 0; i < statNumbers.length; i++) {
      await expect(stats.nth(i).locator('.stat-number')).toContainText(statNumbers[i]);
    }
  });

  test('should have animated phone mockup @desktop @animations', async ({ page }) => {
    const phoneMockup = page.locator('.phone-mockup');
    await expect(phoneMockup).toBeVisible();
    
    // Check phone interface elements
    await expect(phoneMockup.locator('.balance-card')).toBeVisible();
    await expect(phoneMockup.locator('.action-btn')).toHaveCount(4);
    await expect(phoneMockup.locator('.account-item')).toHaveCount(3);
    
    // Test hover effect on phone mockup
    await phoneMockup.hover();
    await page.waitForTimeout(500);
    
    // Check floating cards are visible
    const floatingCards = page.locator('.floating-card');
    await expect(floatingCards).toHaveCount(3);
  });

  test('should display features section correctly @desktop @content', async ({ page }) => {
    const featuresSection = page.locator('.features');
    const featureCards = featuresSection.locator('.feature-card');
    
    await expect(featureCards).toHaveCount(6);
    
    const expectedFeatures = [
      'Account Linking',
      'Interoperable Transfers', 
      'Smart Bill Payments',
      'Group Savings',
      'Financial Analytics',
      'Enterprise Security'
    ];
    
    for (let i = 0; i < expectedFeatures.length; i++) {
      const card = featureCards.nth(i);
      await expect(card.locator('.feature-title')).toHaveText(expectedFeatures[i]);
      await expect(card.locator('.feature-icon')).toBeVisible();
      await expect(card.locator('.feature-description')).toBeVisible();
      await expect(card.locator('.benefit')).toHaveCount(2);
    }
  });

  test('should have working pricing section @desktop @content', async ({ page }) => {
    const pricingSection = page.locator('.pricing');
    const pricingCards = pricingSection.locator('.pricing-card');
    
    await expect(pricingCards).toHaveCount(3);
    
    // Check pricing plans
    const plans = ['Personal', 'Business', 'Enterprise'];
    const prices = ['$0', '$29', 'Custom'];
    
    for (let i = 0; i < plans.length; i++) {
      const card = pricingCards.nth(i);
      await expect(card.locator('.pricing-title')).toHaveText(plans[i]);
      
      if (i < 2) {
        await expect(card.locator('.amount')).toHaveText(prices[i]);
      }
      
      // Check features list
      const features = card.locator('.pricing-features .feature');
      expect(await features.count()).toBeGreaterThan(0);
    }
    
    // Check featured plan
    const featuredCard = pricingCards.nth(1);
    await expect(featuredCard).toHaveClass(/featured/);
    await expect(featuredCard.locator('.pricing-badge')).toHaveText('Most Popular');
  });

  test('should have working CTA buttons @desktop @interactions', async ({ page }) => {
    // Test hero CTA buttons
    const heroDownloadBtn = page.locator('.hero .btn-primary').first();
    await heroDownloadBtn.click();
    await page.waitForTimeout(500);
    
    // Test CTA section buttons
    const ctaSection = page.locator('.cta');
    const ctaButtons = ctaSection.locator('.btn');
    
    await expect(ctaButtons).toHaveCount(2);
    
    for (let i = 0; i < await ctaButtons.count(); i++) {
      const button = ctaButtons.nth(i);
      await expect(button).toBeVisible();
      
      // Test button click
      await button.click();
      await page.waitForTimeout(500);
    }
  });

  test('should have glassmorphism effects @desktop @visual', async ({ page }) => {
    // Check navbar glassmorphism
    const navbar = page.locator('.navbar');
    expect(await hasGlassmorphism(navbar)).toBe(true);
    
    // Check feature cards glassmorphism
    const featureCards = page.locator('.feature-card');
    const cardCount = await featureCards.count();
    
    for (let i = 0; i < Math.min(cardCount, 3); i++) {
      const card = featureCards.nth(i);
      expect(await hasGlassmorphism(card)).toBe(true);
    }
  });

  test('should have smooth animations @desktop @animations', async ({ page }) => {
    // Test scroll-triggered animations
    const featuresSection = page.locator('.features');
    
    // Scroll to features section
    await featuresSection.scrollIntoViewIfNeeded();
    await waitForAnimations(page);
    
    // Check if feature cards are visible (animated in)
    const featureCards = page.locator('.feature-card');
    for (let i = 0; i < Math.min(await featureCards.count(), 3); i++) {
      await expect(featureCards.nth(i)).toBeVisible();
    }
  });

  test('should be responsive @mobile @responsive', async ({ page }) => {
    const viewports = [
      { width: 375, height: 667 }, // iPhone SE
      { width: 768, height: 1024 }, // iPad
      { width: 1024, height: 768 }  // Desktop small
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(500);
      
      // Check main sections are still visible
      await expect(page.locator('.hero')).toBeVisible();
      await expect(page.locator('.features')).toBeVisible();
      
      if (viewport.width <= 768) {
        // Mobile: check mobile menu toggle is visible
        const mobileToggle = page.locator('.mobile-menu-toggle');
        await expect(mobileToggle).toBeVisible();
        
        // Desktop nav should be hidden
        const navMenu = page.locator('.nav-menu');
        const isHidden = await navMenu.evaluate(el => {
          const style = window.getComputedStyle(el);
          return style.display === 'none' || style.visibility === 'hidden';
        });
        expect(isHidden).toBe(true);
      }
    }
  });

  test('should have working footer @desktop @content', async ({ page }) => {
    const footer = page.locator('.footer');
    await expect(footer).toBeVisible();
    
    // Check footer brand
    await expect(footer.locator('.logo')).toBeVisible();
    await expect(footer.locator('.footer-description')).toBeVisible();
    
    // Check footer columns
    const footerColumns = footer.locator('.footer-column');
    await expect(footerColumns).toHaveCount(3);
    
    const expectedColumns = ['Product', 'Company', 'Support'];
    for (let i = 0; i < expectedColumns.length; i++) {
      await expect(footerColumns.nth(i).locator('h4')).toHaveText(expectedColumns[i]);
    }
    
    // Check footer bottom
    await expect(footer.locator('.footer-bottom')).toBeVisible();
    await expect(footer.locator('.footer-legal a')).toHaveCount(2);
  });

  test('should handle form interactions @desktop @forms', async ({ page }) => {
    // Test newsletter signup (if exists)
    const emailInputs = page.locator('input[type="email"]');
    
    if (await emailInputs.count() > 0) {
      const emailInput = emailInputs.first();
      await emailInput.fill('<EMAIL>');
      await expect(emailInput).toHaveValue('<EMAIL>');
    }
    
    // Test contact form (if exists)
    const contactForms = page.locator('form');
    
    if (await contactForms.count() > 0) {
      const form = contactForms.first();
      const inputs = form.locator('input, textarea');
      
      for (let i = 0; i < Math.min(await inputs.count(), 3); i++) {
        const input = inputs.nth(i);
        const inputType = await input.getAttribute('type') || 'text';
        
        if (inputType === 'email') {
          await input.fill('<EMAIL>');
        } else if (inputType === 'text') {
          await input.fill('Test User');
        } else if (await input.evaluate(el => el.tagName.toLowerCase()) === 'textarea') {
          await input.fill('Test message content');
        }
      }
    }
  });

  test('should load without errors @desktop @smoke', async ({ page }) => {
    const errors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.reload();
    await waitForAnimations(page);
    
    expect(errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404')
    )).toHaveLength(0);
  });
});

import { defineConfig } from 'vite'
import { resolve } from 'path'
import legacy from '@vitejs/plugin-legacy'

export default defineConfig({
  // Base public path
  base: '/',
  
  // Development server configuration
  server: {
    port: 8000,
    host: true,
    open: true,
    cors: true,
    hmr: {
      overlay: true
    }
  },
  
  // Preview server configuration
  preview: {
    port: 8000,
    host: true,
    open: true
  },
  
  // Build configuration
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    minify: 'terser',
    target: 'es2015',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        individual: resolve(__dirname, 'individual/index.html'),
        business: resolve(__dirname, 'business/index.html'),
        marketing: resolve(__dirname, 'marketing/index.html'),
        mobile: resolve(__dirname, 'mobile/index.html'),
        admin: resolve(__dirname, 'admin/index.html'),
        'design-system': resolve(__dirname, 'design-system/index.html')
      },
      output: {
        manualChunks: {
          vendor: ['gsap'],
          utils: ['./assets/js/hub.js']
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  
  // CSS configuration
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "./assets/css/design-system.css";`
      }
    }
  },
  
  // Plugins
  plugins: [
    legacy({
      targets: ['defaults', 'not IE 11']
    })
  ],
  
  // Asset handling
  assetsInclude: ['**/*.woff', '**/*.woff2', '**/*.ttf', '**/*.eot'],
  
  // Optimization
  optimizeDeps: {
    include: ['gsap', 'gsap/ScrollTrigger']
  },
  
  // Define global constants
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_DATE__: JSON.stringify(new Date().toISOString())
  }
})

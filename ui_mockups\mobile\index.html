<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>UniversalWallet - Mobile App</title>
    <meta name="description" content="UniversalWallet mobile app interface mockup">
    
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <link rel="stylesheet" href="css/mobile.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#06EAAF">
</head>
<body>
    <!-- Mobile App Container -->
    <div class="mobile-app">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span class="time">9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-level"></div>
                </div>
            </div>
        </div>

        <!-- App Header -->
        <header class="app-header">
            <div class="header-content">
                <a href="../index.html" class="back-to-hub-mobile" title="Back to Hub">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="user-greeting">
                    <div class="greeting-text">Good morning</div>
                    <div class="user-name">John Doe</div>
                </div>
                <div class="header-actions">
                    <button class="header-btn" id="notifications-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-dot"></span>
                    </button>
                    <button class="header-btn" id="profile-btn">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="Profile">
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Balance Card -->
            <section class="balance-section">
                <div class="balance-card">
                    <div class="balance-header">
                        <span class="balance-label">Total Balance</span>
                        <button class="balance-toggle">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="balance-amount">
                        <span class="currency">$</span>
                        <span class="amount">2,847.50</span>
                    </div>
                    <div class="balance-accounts">
                        <div class="account-chip ecocash">
                            <span class="chip-icon">EC</span>
                            <span class="chip-amount">$1,250</span>
                        </div>
                        <div class="account-chip onemoney">
                            <span class="chip-icon">OM</span>
                            <span class="chip-amount">$897</span>
                        </div>
                        <div class="account-chip bank">
                            <span class="chip-icon">ZB</span>
                            <span class="chip-amount">$700</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <div class="actions-grid">
                    <button class="action-btn" data-action="send">
                        <div class="action-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <span class="action-label">Send</span>
                    </button>
                    <button class="action-btn" data-action="receive">
                        <div class="action-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <span class="action-label">Receive</span>
                    </button>
                    <button class="action-btn" data-action="bills">
                        <div class="action-icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <span class="action-label">Bills</span>
                    </button>
                    <button class="action-btn" data-action="scan">
                        <div class="action-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <span class="action-label">Scan</span>
                    </button>
                </div>
            </section>

            <!-- Services Grid -->
            <section class="services-section">
                <div class="section-header">
                    <h2>Services</h2>
                    <button class="see-all-btn">See All</button>
                </div>
                <div class="services-grid">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="service-content">
                            <div class="service-title">Savings Groups</div>
                            <div class="service-subtitle">3 active groups</div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="service-content">
                            <div class="service-title">Virtual Cards</div>
                            <div class="service-subtitle">2 active cards</div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="service-content">
                            <div class="service-title">Analytics</div>
                            <div class="service-subtitle">View insights</div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="service-content">
                            <div class="service-title">Requests</div>
                            <div class="service-subtitle">Money requests</div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Transactions -->
            <section class="transactions-section">
                <div class="section-header">
                    <h2>Recent</h2>
                    <button class="see-all-btn">See All</button>
                </div>
                <div class="transactions-list">
                    <div class="transaction-item">
                        <div class="transaction-icon received">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-title">Jane Smith</div>
                            <div class="transaction-subtitle">Today, 2:30 PM</div>
                        </div>
                        <div class="transaction-amount positive">+$150.00</div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-icon sent">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-title">Bob's Cafe</div>
                            <div class="transaction-subtitle">Yesterday, 9:15 AM</div>
                        </div>
                        <div class="transaction-amount negative">-$5.50</div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-icon bill">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-title">ZESA Bill</div>
                            <div class="transaction-subtitle">Oct 15, 4:20 PM</div>
                        </div>
                        <div class="transaction-amount negative">-$75.00</div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-icon group">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-title">Family Savings</div>
                            <div class="transaction-subtitle">Oct 14, 6:00 PM</div>
                        </div>
                        <div class="transaction-amount negative">-$50.00</div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <button class="nav-item active" data-page="home">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </button>
            <button class="nav-item" data-page="accounts">
                <i class="fas fa-credit-card"></i>
                <span>Accounts</span>
            </button>
            <button class="nav-item" data-page="transfer">
                <i class="fas fa-exchange-alt"></i>
                <span>Transfer</span>
            </button>
            <button class="nav-item" data-page="history">
                <i class="fas fa-history"></i>
                <span>History</span>
            </button>
            <button class="nav-item" data-page="more">
                <i class="fas fa-ellipsis-h"></i>
                <span>More</span>
            </button>
        </nav>
    </div>

    <!-- Mobile Overlay for Notifications -->
    <div class="mobile-overlay" id="notifications-overlay">
        <div class="overlay-header">
            <h3>Notifications</h3>
            <button class="close-overlay">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="overlay-content">
            <div class="notification-item">
                <div class="notification-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">Payment Received</div>
                    <div class="notification-text">You received $150.00 from Jane Smith</div>
                    <div class="notification-time">2 minutes ago</div>
                </div>
            </div>
            
            <div class="notification-item">
                <div class="notification-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">Group Savings</div>
                    <div class="notification-text">Monthly contribution reminder</div>
                    <div class="notification-time">1 hour ago</div>
                </div>
            </div>
            
            <div class="notification-item">
                <div class="notification-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">Bill Due</div>
                    <div class="notification-text">ZESA bill due in 3 days</div>
                    <div class="notification-time">3 hours ago</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Touch Feedback Overlay -->
    <div class="touch-feedback" id="touch-feedback"></div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="js/mobile.js"></script>
</body>
</html>

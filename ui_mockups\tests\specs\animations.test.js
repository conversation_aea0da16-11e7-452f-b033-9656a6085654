// Animation and Performance Tests
const { test, expect } = require('@playwright/test');
const { waitForAnimations, checkAnimationPerformance } = require('../utils/test-helpers');

test.describe('Animation and Performance Tests', () => {
  
  test.describe('Marketing Website Animations', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/marketing/');
    });

    test('should have smooth hero animations @desktop @animations', async ({ page }) => {
      // Test hero entrance animations
      await page.reload();
      
      // Check if GSAP is loaded
      const gsapLoaded = await page.evaluate(() => typeof window.gsap !== 'undefined');
      expect(gsapLoaded).toBe(true);
      
      // Wait for animations to complete
      await waitForAnimations(page);
      
      // Check if hero elements are visible after animation
      await expect(page.locator('.hero-title')).toBeVisible();
      await expect(page.locator('.hero-description')).toBeVisible();
      await expect(page.locator('.hero-actions')).toBeVisible();
    });

    test('should have scroll-triggered animations @desktop @animations', async ({ page }) => {
      await waitForAnimations(page);
      
      // Scroll to features section
      const featuresSection = page.locator('.features');
      await featuresSection.scrollIntoViewIfNeeded();
      
      // Wait for scroll-triggered animations
      await page.waitForTimeout(1000);
      
      // Check if feature cards are visible
      const featureCards = page.locator('.feature-card');
      for (let i = 0; i < Math.min(await featureCards.count(), 3); i++) {
        await expect(featureCards.nth(i)).toBeVisible();
      }
    });

    test('should have smooth parallax effects @desktop @animations', async ({ page }) => {
      await waitForAnimations(page);
      
      // Check if hero shapes exist
      const heroShapes = page.locator('.shape');
      await expect(heroShapes).toHaveCount(3);
      
      // Scroll and check if shapes move (parallax effect)
      const initialPosition = await heroShapes.first().evaluate(el => {
        const transform = window.getComputedStyle(el).transform;
        return transform;
      });
      
      await page.evaluate(() => window.scrollBy(0, 500));
      await page.waitForTimeout(500);
      
      const newPosition = await heroShapes.first().evaluate(el => {
        const transform = window.getComputedStyle(el).transform;
        return transform;
      });
      
      // Transform should change due to parallax
      expect(newPosition).not.toBe(initialPosition);
    });

    test('should have hover animations @desktop @animations', async ({ page }) => {
      await waitForAnimations(page);
      
      // Test feature card hover
      const featureCard = page.locator('.feature-card').first();
      
      const initialTransform = await featureCard.evaluate(el => 
        window.getComputedStyle(el).transform
      );
      
      await featureCard.hover();
      await page.waitForTimeout(500);
      
      const hoverTransform = await featureCard.evaluate(el => 
        window.getComputedStyle(el).transform
      );
      
      expect(hoverTransform).not.toBe(initialTransform);
    });
  });

  test.describe('Dashboard Animations', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/individual/');
    });

    test('should have smooth page entrance @desktop @animations', async ({ page }) => {
      await page.reload();
      
      // Check GSAP is loaded
      const gsapLoaded = await page.evaluate(() => typeof window.gsap !== 'undefined');
      expect(gsapLoaded).toBe(true);
      
      await waitForAnimations(page);
      
      // Check if main elements are visible
      await expect(page.locator('.sidebar')).toBeVisible();
      await expect(page.locator('.balance-overview')).toBeVisible();
      await expect(page.locator('.quick-actions')).toBeVisible();
    });

    test('should have staggered card animations @desktop @animations', async ({ page }) => {
      await page.reload();
      await waitForAnimations(page);
      
      // Check if balance cards are visible
      const balanceCards = page.locator('.balance-card');
      for (let i = 0; i < await balanceCards.count(); i++) {
        await expect(balanceCards.nth(i)).toBeVisible();
      }
      
      // Check if action cards are visible
      const actionCards = page.locator('.action-card');
      for (let i = 0; i < Math.min(await actionCards.count(), 3); i++) {
        await expect(actionCards.nth(i)).toBeVisible();
      }
    });

    test('should have smooth page transitions @desktop @animations', async ({ page }) => {
      await waitForAnimations(page);
      
      // Navigate to accounts page
      const accountsMenuItem = page.locator('.menu-item[data-page="accounts"]');
      await accountsMenuItem.click();
      
      // Wait for transition
      await page.waitForTimeout(1000);
      
      // Check page title changed
      const pageTitle = page.locator('#page-title');
      await expect(pageTitle).toHaveText('My Accounts');
    });

    test('should have counter animations @desktop @animations', async ({ page }) => {
      await page.reload();
      
      // Wait for counter animations to start
      await page.waitForTimeout(1000);
      
      // Check if balance amounts are visible and have values
      const amounts = page.locator('.amount, .balance-value');
      for (let i = 0; i < Math.min(await amounts.count(), 3); i++) {
        const amount = amounts.nth(i);
        await expect(amount).toBeVisible();
        
        const value = await amount.textContent();
        expect(value).not.toBe('');
        expect(value).not.toBe('0');
      }
    });
  });

  test.describe('Mobile Animations', () => {
    test.beforeEach(async ({ page }) => {
      await page.goto('/mobile/');
    });

    test('should have mobile entrance animations @mobile @animations', async ({ page }) => {
      await page.reload();
      
      // Check GSAP is loaded
      const gsapLoaded = await page.evaluate(() => typeof window.gsap !== 'undefined');
      expect(gsapLoaded).toBe(true);
      
      await waitForAnimations(page);
      
      // Check mobile app container is visible
      await expect(page.locator('.mobile-app')).toBeVisible();
      await expect(page.locator('.status-bar')).toBeVisible();
      await expect(page.locator('.balance-card')).toBeVisible();
    });

    test('should have touch animations @mobile @animations', async ({ page }) => {
      await waitForAnimations(page);
      
      // Test action button touch animation
      const actionButton = page.locator('.action-btn').first();
      
      // Get initial scale
      const initialTransform = await actionButton.evaluate(el => 
        window.getComputedStyle(el).transform
      );
      
      // Simulate touch
      await actionButton.click();
      await page.waitForTimeout(200);
      
      // Transform should have changed during touch
      // (Note: might return to original after animation completes)
      await expect(actionButton).toBeVisible();
    });

    test('should have bottom nav animations @mobile @animations', async ({ page }) => {
      await waitForAnimations(page);
      
      // Test navigation animation
      const navItems = page.locator('.nav-item');
      const accountsItem = navItems.nth(1);
      
      await accountsItem.click();
      await page.waitForTimeout(500);
      
      // Check active state animation
      await expect(accountsItem).toHaveClass(/active/);
    });
  });

  test.describe('Performance Tests', () => {
    test('should maintain 60fps during animations @desktop @performance', async ({ page }) => {
      await page.goto('/marketing/');
      
      // Test animation performance
      const fps = await checkAnimationPerformance(page, async () => {
        const featureCard = page.locator('.feature-card').first();
        await featureCard.hover();
        await page.waitForTimeout(1000);
      });
      
      // Should maintain reasonable frame rate (allowing some tolerance)
      expect(fps).toBeGreaterThan(30);
    });

    test('should load animations without blocking @desktop @performance', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/individual/');
      await waitForAnimations(page);
      
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time
      expect(loadTime).toBeLessThan(5000);
      
      // Check if page is interactive
      const sidebar = page.locator('.sidebar');
      await expect(sidebar).toBeVisible();
    });

    test('should handle rapid interactions @desktop @performance', async ({ page }) => {
      await page.goto('/individual/');
      await waitForAnimations(page);
      
      // Rapidly click menu items
      const menuItems = page.locator('.menu-item');
      
      for (let i = 0; i < Math.min(await menuItems.count(), 5); i++) {
        await menuItems.nth(i).click();
        await page.waitForTimeout(100); // Rapid clicking
      }
      
      // Should still be responsive
      const pageTitle = page.locator('#page-title');
      await expect(pageTitle).toBeVisible();
    });

    test('should not have memory leaks @desktop @performance', async ({ page }) => {
      await page.goto('/marketing/');
      
      // Get initial memory usage
      const initialMetrics = await page.evaluate(() => {
        if (performance.memory) {
          return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize
          };
        }
        return null;
      });
      
      // Trigger multiple animations
      for (let i = 0; i < 5; i++) {
        await page.reload();
        await waitForAnimations(page);
        
        // Scroll to trigger animations
        await page.evaluate(() => window.scrollTo(0, 1000));
        await page.waitForTimeout(500);
        await page.evaluate(() => window.scrollTo(0, 0));
        await page.waitForTimeout(500);
      }
      
      // Check memory usage hasn't grown excessively
      const finalMetrics = await page.evaluate(() => {
        if (performance.memory) {
          return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize
          };
        }
        return null;
      });
      
      if (initialMetrics && finalMetrics) {
        const memoryGrowth = finalMetrics.usedJSHeapSize - initialMetrics.usedJSHeapSize;
        const growthPercentage = (memoryGrowth / initialMetrics.usedJSHeapSize) * 100;
        
        // Memory growth should be reasonable (less than 100% increase)
        expect(growthPercentage).toBeLessThan(100);
      }
    });
  });

  test.describe('Cross-Browser Animation Support', () => {
    test('should work in different browsers @desktop @animations', async ({ page, browserName }) => {
      await page.goto('/marketing/');
      await waitForAnimations(page);
      
      // Check if animations work across browsers
      const heroTitle = page.locator('.hero-title');
      await expect(heroTitle).toBeVisible();
      
      // Test CSS animations support
      const supportsAnimations = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.animation = 'test 1s';
        return testEl.style.animation !== '';
      });
      
      expect(supportsAnimations).toBe(true);
      
      // Test transform support
      const supportsTransform = await page.evaluate(() => {
        const testEl = document.createElement('div');
        testEl.style.transform = 'translateX(10px)';
        return testEl.style.transform !== '';
      });
      
      expect(supportsTransform).toBe(true);
    });

    test('should handle reduced motion preferences @desktop @accessibility', async ({ page }) => {
      // Set reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' });
      
      await page.goto('/marketing/');
      await waitForAnimations(page);
      
      // Content should still be visible even with reduced motion
      await expect(page.locator('.hero-title')).toBeVisible();
      await expect(page.locator('.features')).toBeVisible();
    });
  });
});

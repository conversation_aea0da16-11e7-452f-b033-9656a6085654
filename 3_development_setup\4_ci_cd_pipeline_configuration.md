# 4. CI/CD Pipeline Configuration

## 🚀 **CI/CD Overview**

This document outlines the comprehensive Continuous Integration and Continuous Deployment pipeline for the UniversalWallet platform, ensuring automated testing, security scanning, quality gates, and deployment across all environments.

## 🏗️ **Pipeline Architecture**

### **Pipeline Stages**
```
Source Code → Build → Test → Security Scan → Quality Gate → Deploy → Monitor
     ↓         ↓      ↓         ↓            ↓           ↓        ↓
   GitHub   Docker  Unit/     SAST/DAST    SonarQube   K8s     Grafana
            Build   Integration  Snyk      Coverage    Deploy  Monitoring
```

### **Environment Strategy**
- **Development**: Feature branch deployments for testing
- **Staging**: Integration testing and UAT environment
- **Production**: Live production environment with blue-green deployment

---

## 🔧 **GitHub Actions Configuration**

### **Main CI/CD Pipeline**

#### **.github/workflows/main.yml**
```yaml
name: UniversalWallet CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: universalwallet

jobs:
  # ============================================================================
  # BUILD AND TEST JOBS
  # ============================================================================
  
  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: universalwallet_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Run unit tests
      run: |
        cd backend
        mvn clean test -Dspring.profiles.active=test
    
    - name: Run integration tests
      run: |
        cd backend
        mvn verify -Dspring.profiles.active=test
    
    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Backend Test Results
        path: backend/target/surefire-reports/*.xml
        reporter: java-junit
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: backend/target/site/jacoco/jacoco.xml
        flags: backend
        name: backend-coverage

  frontend-test:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: |
          frontend-web/package-lock.json
          frontend-mobile/package-lock.json
    
    - name: Install web dependencies
      run: |
        cd frontend-web
        npm ci
    
    - name: Install mobile dependencies
      run: |
        cd frontend-mobile
        npm ci
    
    - name: Lint web application
      run: |
        cd frontend-web
        npm run lint
    
    - name: Lint mobile application
      run: |
        cd frontend-mobile
        npm run lint
    
    - name: Type check
      run: |
        cd frontend-web
        npm run type-check
        cd ../frontend-mobile
        npm run type-check
    
    - name: Run web tests
      run: |
        cd frontend-web
        npm run test:coverage
    
    - name: Run mobile tests
      run: |
        cd frontend-mobile
        npm run test:coverage
    
    - name: Upload web coverage
      uses: codecov/codecov-action@v3
      with:
        directory: frontend-web/coverage
        flags: frontend-web
        name: web-coverage
    
    - name: Upload mobile coverage
      uses: codecov/codecov-action@v3
      with:
        directory: frontend-mobile/coverage
        flags: frontend-mobile
        name: mobile-coverage

  # ============================================================================
  # SECURITY SCANNING JOBS
  # ============================================================================
  
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/maven@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high
    
    - name: Run OWASP Dependency Check
      run: |
        cd backend
        mvn org.owasp:dependency-check-maven:check
    
    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: backend/target/dependency-check-report.sarif
    
    - name: Run Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/owasp-top-ten
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # ============================================================================
  # QUALITY GATE JOBS
  # ============================================================================
  
  sonarqube-analysis:
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache SonarQube packages
      uses: actions/cache@v3
      with:
        path: ~/.sonar/cache
        key: ${{ runner.os }}-sonar
        restore-keys: ${{ runner.os }}-sonar
    
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Run SonarQube analysis
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      run: |
        cd backend
        mvn clean verify sonar:sonar \
          -Dsonar.projectKey=universalwallet \
          -Dsonar.host.url=${{ secrets.SONAR_HOST_URL }} \
          -Dsonar.login=${{ secrets.SONAR_TOKEN }}

  # ============================================================================
  # BUILD AND DEPLOY JOBS
  # ============================================================================
  
  build-and-push:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: [security-scan, sonarqube-analysis]
    if: github.event_name != 'pull_request'
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push backend image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ steps.meta.outputs.tags }}-backend
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push web frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend-web
        push: true
        tags: ${{ steps.meta.outputs.tags }}-web
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}
    
    - name: Deploy to staging
      run: |
        envsubst < k8s/staging/deployment.yaml | kubectl apply -f -
        kubectl rollout status deployment/universalwallet-backend -n staging
        kubectl rollout status deployment/universalwallet-web -n staging
      env:
        IMAGE_TAG: ${{ needs.build-and-push.outputs.image-tag }}
    
    - name: Run smoke tests
      run: |
        kubectl wait --for=condition=ready pod -l app=universalwallet-backend -n staging --timeout=300s
        curl -f https://staging-api.universalwallet.co.zw/health || exit 1

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}
    
    - name: Blue-Green Deployment
      run: |
        # Deploy to green environment
        envsubst < k8s/production/deployment-green.yaml | kubectl apply -f -
        kubectl rollout status deployment/universalwallet-backend-green -n production
        
        # Run health checks
        kubectl wait --for=condition=ready pod -l app=universalwallet-backend-green -n production --timeout=600s
        
        # Switch traffic to green
        kubectl patch service universalwallet-backend -n production -p '{"spec":{"selector":{"version":"green"}}}'
        
        # Clean up blue environment
        kubectl delete deployment universalwallet-backend-blue -n production --ignore-not-found=true
      env:
        IMAGE_TAG: ${{ needs.build-and-push.outputs.image-tag }}
    
    - name: Post-deployment verification
      run: |
        sleep 30
        curl -f https://api.universalwallet.co.zw/health || exit 1
        curl -f https://api.universalwallet.co.zw/metrics || exit 1
```

---

## 🐳 **Docker Configuration**

### **Backend Dockerfile**
```dockerfile
# backend/Dockerfile
FROM openjdk:17-jdk-slim as builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

RUN apt-get update && apt-get install -y maven
RUN mvn clean package -DskipTests

FROM openjdk:17-jre-slim

RUN addgroup --system spring && adduser --system spring --ingroup spring
USER spring:spring

WORKDIR /app
COPY --from=builder /app/target/universalwallet-*.jar app.jar

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### **Frontend Dockerfile**
```dockerfile
# frontend-web/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine

COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

---

## ☸️ **Kubernetes Deployment**

### **Production Deployment**
```yaml
# k8s/production/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: universalwallet-backend
  namespace: production
  labels:
    app: universalwallet-backend
    version: blue
spec:
  replicas: 3
  selector:
    matchLabels:
      app: universalwallet-backend
      version: blue
  template:
    metadata:
      labels:
        app: universalwallet-backend
        version: blue
    spec:
      containers:
      - name: backend
        image: ${REGISTRY}/${IMAGE_NAME}-backend:${IMAGE_TAG}
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: universalwallet-backend
  namespace: production
spec:
  selector:
    app: universalwallet-backend
    version: blue
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

---

## 📊 **Monitoring and Alerting**

### **Prometheus Configuration**
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'universalwallet-backend'
    static_configs:
      - targets: ['universalwallet-backend:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s

  - job_name: 'universalwallet-web'
    static_configs:
      - targets: ['universalwallet-web:80']
    metrics_path: '/metrics'
    scrape_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### **Alert Rules**
```yaml
# monitoring/alert_rules.yml
groups:
- name: universalwallet.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }} seconds"

  - alert: DatabaseConnectionFailure
    expr: up{job="postgres"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database connection failure"
      description: "Cannot connect to PostgreSQL database"
```

**This comprehensive CI/CD pipeline ensures automated, secure, and reliable deployment of the UniversalWallet platform across all environments.** 🚀
